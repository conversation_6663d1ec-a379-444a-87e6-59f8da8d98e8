"""
Utility functions for converting Word documents to PDF.
"""
import os
import sys
import platform
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def convert_with_docx2pdf(input_path, output_path):
    """
    Convert a Word document to PDF using docx2pdf.
    This requires Microsoft Word to be installed on Windows.
    
    Args:
        input_path (str): Path to the input Word document
        output_path (str): Path to save the output PDF
        
    Returns:
        bool: True if conversion was successful, False otherwise
    """
    try:
        from docx2pdf import convert
        logger.info(f"Converting {input_path} to {output_path} using docx2pdf")
        convert(input_path, output_path)
        return os.path.exists(output_path)
    except Exception as e:
        logger.error(f"docx2pdf conversion failed: {str(e)}")
        return False

def convert_with_python_docx(input_path, output_path):
    """
    Convert a Word document to PDF using python-docx and reportlab.
    This is a fallback method that extracts text only (no formatting or images).
    
    Args:
        input_path (str): Path to the input Word document
        output_path (str): Path to save the output PDF
        
    Returns:
        bool: True if conversion was successful, False otherwise
    """
    try:
        import docx
        from reportlab.lib.pagesizes import letter
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
        
        logger.info(f"Converting {input_path} to {output_path} using python-docx and reportlab")
        
        # Read the Word document
        doc = docx.Document(input_path)
        
        # Create a PDF
        pdf = SimpleDocTemplate(
            output_path,
            pagesize=letter,
            title=f"Converted from {os.path.basename(input_path)}"
        )
        
        # Set up styles
        styles = getSampleStyleSheet()
        styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=styles['Title'],
            fontSize=24,
            spaceAfter=24
        ))
        
        # Extract text and create PDF content
        content = []
        content.append(Paragraph(f"Converted from: {os.path.basename(input_path)}", styles['CustomTitle']))
        content.append(Spacer(1, 12))
        
        # Add paragraphs from the Word document
        for para in doc.paragraphs:
            if para.text:
                content.append(Paragraph(para.text, styles['Normal']))
                content.append(Spacer(1, 6))
        
        # Build the PDF
        pdf.build(content)
        
        return os.path.exists(output_path)
    except Exception as e:
        logger.error(f"python-docx conversion failed: {str(e)}")
        return False

def create_error_pdf(input_path, output_path, error_message):
    """
    Create a PDF with an error message when conversion fails.
    
    Args:
        input_path (str): Path to the input Word document
        output_path (str): Path to save the output PDF
        error_message (str): Error message to include in the PDF
        
    Returns:
        bool: True if PDF creation was successful, False otherwise
    """
    try:
        from reportlab.lib.pagesizes import letter
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
        
        logger.info(f"Creating error PDF for {input_path}")
        
        # Create a PDF
        pdf = SimpleDocTemplate(
            output_path,
            pagesize=letter,
            title=f"Conversion Error - {os.path.basename(input_path)}"
        )
        
        # Set up styles
        styles = getSampleStyleSheet()
        styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=styles['Title'],
            fontSize=24,
            spaceAfter=24
        ))
        
        # Create content
        content = []
        content.append(Paragraph(f"Conversion Error - {os.path.basename(input_path)}", styles['CustomTitle']))
        content.append(Spacer(1, 12))
        content.append(Paragraph("The Word document could not be converted to PDF.", styles['Normal']))
        content.append(Spacer(1, 12))
        content.append(Paragraph(f"Error details: {error_message}", styles['Normal']))
        content.append(Spacer(1, 12))
        content.append(Paragraph("Please try again or use another conversion method.", styles['Normal']))
        
        # Build the PDF
        pdf.build(content)
        
        return os.path.exists(output_path)
    except Exception as e:
        logger.error(f"Error PDF creation failed: {str(e)}")
        return False

def convert_docx_to_pdf(input_path, output_path):
    """
    Convert a Word document to PDF using the best available method.
    
    Args:
        input_path (str): Path to the input Word document
        output_path (str): Path to save the output PDF
        
    Returns:
        bool: True if conversion was successful, False otherwise
    """
    # Try docx2pdf first (best results but requires MS Word on Windows)
    if platform.system() == 'Windows':
        if convert_with_docx2pdf(input_path, output_path):
            return True
    
    # Try python-docx fallback (text only)
    if convert_with_python_docx(input_path, output_path):
        return True
    
    # Create error PDF as last resort
    return create_error_pdf(input_path, output_path, "All conversion methods failed")
