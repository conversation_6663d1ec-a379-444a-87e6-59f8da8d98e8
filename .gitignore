# Node.js / Next.js
# Frontend dependencies
/frontend/node_modules
/frontend/.pnp
/frontend/.pnp.js

# Frontend testing
/frontend/coverage

# Frontend next.js
/frontend/.next/
/frontend/out/

# Frontend production
/frontend/build

# Frontend misc
/frontend/.DS_Store
/frontend/*.pem

# Frontend debug
/frontend/npm-debug.log*
/frontend/yarn-debug.log*
/frontend/yarn-error.log*

# Frontend local env files
/frontend/.env*.local

# Frontend vercel
/frontend/.vercel

# Frontend typescript
/frontend/*.tsbuildinfo
/frontend/next-env.d.ts

# Python / FastAPI
# Backend byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Backend C extensions
*.so

# Backend distribution / packaging
.Python
/backend/build/
/backend/develop-eggs/
/backend/dist/
/backend/downloads/
/backend/eggs/
/backend/.eggs/
/backend/lib/
/backend/lib64/
/backend/parts/
/backend/sdist/
/backend/var/
/backend/wheels/
/backend/*.egg-info/
/backend/.installed.cfg
/backend/*.egg
/backend/MANIFEST

# Backend PyInstaller
/backend/*.manifest
/backend/*.spec

# Backend installer logs
/backend/pip-log.txt
/backend/pip-delete-this-directory.txt

# Backend unit test / coverage reports
/backend/htmlcov/
/backend/.tox/
/backend/.nox/
/backend/.coverage
/backend/.coverage.*
/backend/.cache
/backend/nosetests.xml
/backend/coverage.xml
/backend/*.cover
/backend/*.py,cover
/backend/.hypothesis/
/backend/.pytest_cache/
/backend/cover/

# Backend translations
/backend/*.mo
/backend/*.pot

# Backend Django stuff:
/backend/*.log
/backend/local_settings.py
/backend/db.sqlite3
/backend/db.sqlite3-journal

# Backend Flask stuff:
/backend/instance/
/backend/.webassets-cache

# Backend Scrapy stuff:
/backend/.scrapy

# Backend Sphinx documentation
/backend/docs/_build/

# Backend PyBuilder
/backend/.pybuilder/
/backend/target/

# Backend Jupyter Notebook
/backend/.ipynb_checkpoints

# Backend IPython
/backend/profile_default/
/backend/ipython_config.py

# Backend pyenv
/backend/.python-version

# Backend pipenv
/backend/Pipfile.lock

# Backend poetry
/backend/poetry.lock

# Backend pdm
/backend/.pdm.toml

# Backend PEP 582
/backend/__pypackages__/

# Backend Celery stuff
/backend/celerybeat-schedule
/backend/celerybeat.pid

# Backend SageMath parsed files
/backend/*.sage.py

# Backend environments
/backend/.env
/backend/.venv
/backend/env/
/backend/venv/
/backend/ENV/
/backend/env.bak/
/backend/venv.bak/

# Backend Spyder project settings
/backend/.spyderproject
/backend/.spyproject

# Backend Rope project settings
/backend/.ropeproject

# Backend mkdocs documentation
/backend/site

# Backend mypy
/backend/.mypy_cache/
/backend/.dmypy.json
/backend/dmypy.json

# Backend Pyre type checker
/backend/.pyre/

# Backend pytype static type analyzer
/backend/.pytype/

# Backend Cython debug symbols
/backend/cython_debug/

# Backend PyCharm
/backend/.idea/

# Backend VS Code
.vscode/

# Temporary files
/temp/
*.tmp
*.temp

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Log files
*.log
logs/
log/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
# Comment in the public line in if your project uses Gatsby and not Next.js
# public

# vuepress build output
.vuepress/dist

# vuepress v2.x temp and cache directory
.temp
.cache

# Docusaurus cache and generated files
.docusaurus

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*
