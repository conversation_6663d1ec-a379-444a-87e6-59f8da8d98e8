"use client";

import { Toaster } from "@/components/ui/sonner";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "next/font/google";
import "./fonts.css";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
});

const sora = Sora({
  variable: "--font-sora",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
});

// Metadata is handled in the head.tsx file for client components

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${sora.variable} font-sans`}>
      <head>
        <title>FreeFox - PDF Utilities</title>
        <meta
          name="description"
          content="All-in-one utility tool for PDF operations"
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${poppins.variable} ${sora.variable} font-sans antialiased`}
        style={{
          fontFamily: "var(--font-sora), ui-sans-serif, system-ui, sans-serif",
        }}
      >
        <Toaster />
        {children}
      </body>
    </html>
  );
}
