"use client";

import { EnhancedFileUpload } from "@/components/EnhancedFileUpload";
import { MainLayout } from "@/components/MainLayout";
import { SuccessConfetti } from "@/components/SuccessConfetti";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { apiPost, downloadFromResponse } from "@/lib/api";
import { motion } from "framer-motion";
import { useState } from "react";
import { toast } from "sonner";

export default function UpscaleImagePage() {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showConfetti, setShowConfetti] = useState(false);
  const [scaleFactor, setScaleFactor] = useState(2.0);
  const [enhanceQuality, setEnhanceQuality] = useState(true);

  const handleFilesSelected = (selectedFiles: File[]) => {
    // With replaceExisting=true, we'll always get just the latest file
    setFiles(selectedFiles);
  };

  const handleUpscale = async () => {
    if (files.length === 0) {
      toast.error("Please select an image file to upscale");
      return;
    }

    setIsProcessing(true);
    setProgress(10);

    try {
      // Create a FormData object to send the file
      const formData = new FormData();
      formData.append("file", files[0]);
      formData.append("scale_factor", scaleFactor.toString());
      formData.append("enhance_quality", enhanceQuality.toString());

      // Simulate progress
      const progressInterval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 80) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 3; // Slower progress for upscaling which takes longer
        });
      }, 300);

      setProgress(30);

      // Use the API utility instead of direct fetch
      const response = await apiPost("upscale-image", formData);

      setProgress(90);

      // Use the utility function to handle the download
      await downloadFromResponse(response, `upscaled_${files[0].name}`);

      setProgress(100);
      setShowConfetti(true);
      toast.success("Image upscaled successfully");
    } catch (error) {
      console.error("Error upscaling image:", error);
      toast.error("Failed to upscale image. Please try again.");
    } finally {
      setTimeout(() => {
        setIsProcessing(false);
        setProgress(0);
      }, 500);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 400, damping: 30 },
    },
  };

  return (
    <MainLayout>
      <SuccessConfetti
        show={showConfetti}
        duration={3000}
        onComplete={() => setShowConfetti(false)}
      />

      <motion.div
        className="max-w-3xl mx-auto space-y-8"
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        <motion.div className="text-center space-y-2" variants={itemVariants}>
          <h1 className="text-3xl font-bold tracking-tight font-display">
            Image Upscaler
          </h1>
          <p className="text-muted-foreground">
            Increase resolution and enhance image quality
          </p>
        </motion.div>

        <motion.div className="space-y-6" variants={itemVariants}>
          <EnhancedFileUpload
            acceptedFileTypes={{
              "image/jpeg": [".jpg", ".jpeg"],
              "image/png": [".png"],
              "image/webp": [".webp"],
              "image/gif": [".gif"],
              "image/bmp": [".bmp"],
              "image/tiff": [".tiff", ".tif"],
            }}
            maxFiles={1}
            onFilesSelected={handleFilesSelected}
            isProcessing={isProcessing}
            processingProgress={progress}
            replaceExisting={true}
          />

          {files.length > 0 && (
            <div className="bg-muted/30 p-4 rounded-lg space-y-4">
              <h3 className="font-medium font-display">Upscaling Options</h3>
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">
                      Scale Factor: {scaleFactor.toFixed(1)}x
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {scaleFactor < 2.0
                        ? "Smaller increase in size"
                        : scaleFactor < 3.0
                        ? "Balanced increase in size"
                        : "Larger increase in size"}
                    </span>
                  </div>
                  <Slider
                    value={[scaleFactor]}
                    min={1.0}
                    max={4.0}
                    step={0.1}
                    onValueChange={(value) => setScaleFactor(value[0])}
                    disabled={isProcessing}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Higher values result in larger images but may take longer to
                    process.
                  </p>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="enhance-quality"
                    checked={enhanceQuality}
                    onCheckedChange={(checked) =>
                      setEnhanceQuality(checked === true)
                    }
                    disabled={isProcessing}
                  />
                  <Label
                    htmlFor="enhance-quality"
                    className="text-sm font-normal"
                  >
                    Enhance image quality
                  </Label>
                </div>
                <p className="text-xs text-muted-foreground">
                  Applies additional enhancements to improve sharpness and
                  contrast.
                </p>
              </div>
            </div>
          )}
          {files.length > 0 && (
            <div className="flex justify-end">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  onClick={handleUpscale}
                  disabled={isProcessing}
                  className="px-8"
                >
                  {isProcessing ? "Upscaling..." : "Upscale Image"}
                </Button>
              </motion.div>
            </div>
          )}
        </motion.div>

        <motion.div
          className="bg-muted/30 p-6 rounded-lg space-y-4"
          variants={itemVariants}
        >
          <h2 className="text-xl font-bold font-display">
            About Image Upscaling
          </h2>
          <div className="space-y-2 text-sm text-muted-foreground">
            <p>
              Our image upscaler uses advanced algorithms to increase the
              resolution of your images while preserving and enhancing quality.
              This is useful for:
            </p>
            <ul className="list-disc list-inside space-y-1">
              <li>Enlarging small images for printing</li>
              <li>Improving the quality of low-resolution images</li>
              <li>
                Creating high-resolution versions of images for professional use
              </li>
              <li>Enhancing details in photos</li>
            </ul>
            <p>
              The upscaling process uses high-quality resampling techniques to
              intelligently add pixels while maintaining sharpness and detail.
              The quality enhancement option applies additional processing to
              improve the final result.
            </p>
            <p className="text-xs mt-2">
              Note: Upscaling has limitations and cannot create details that
              don't exist in the original image. Results are best with images
              that already have good quality.
            </p>
          </div>
        </motion.div>
      </motion.div>
    </MainLayout>
  );
}
