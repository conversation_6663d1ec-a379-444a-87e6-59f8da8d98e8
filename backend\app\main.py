import time

from app.routes import image_tools, pdf_tools
from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# Create FastAPI app with configuration for handling large files
app = FastAPI(
    title="FreeFox API",
    description="API for PDF utilities including merge, compress, and Word to PDF conversion",
    version="1.0.0",
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=[
        "Content-Disposition",
        "X-Compression-Ratio",
        "X-Original-Size",
        "X-Compressed-Size"
    ],
)

# Add middleware for request timing and logging
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response

# Error handling
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    return JSONResponse(
        status_code=500,
        content={"detail": f"An unexpected error occurred: {str(exc)}"},
    )

# Include routers
app.include_router(pdf_tools.router, prefix="/api/v1", tags=["PDF Tools"])
app.include_router(image_tools.router, prefix="/api/v1", tags=["Image Tools"])

# Root endpoint
@app.get("/", tags=["Root"])
async def root():
    return {
        "message": "Welcome to FreeFox API",
        "docs": "/docs",
        "endpoints": {
            # PDF Tools
            "merge_pdf": "/api/v1/merge-pdf",
            "compress_pdf": "/api/v1/compress-pdf",
            "word_to_pdf": "/api/v1/word-to-pdf",
            "split_pdf": "/api/v1/split-pdf",
            "rotate_pdf": "/api/v1/rotate-pdf",
            "pdf_to_word": "/api/v1/pdf-to-word",

            # Image Tools
            "compress_image": "/api/v1/compress-image",
            "convert_image": "/api/v1/convert-image",
            "resize_image": "/api/v1/resize-image",
            "crop_image": "/api/v1/crop-image",
            "upscale_image": "/api/v1/upscale-image",
            "image_to_pdf": "/api/v1/image-to-pdf",
        },
    }
