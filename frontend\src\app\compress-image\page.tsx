"use client";

import { EnhancedFileUpload } from "@/components/EnhancedFileUpload";
import { MainLayout } from "@/components/MainLayout";
import { SuccessConfetti } from "@/components/SuccessConfetti";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { apiPost, downloadFromResponse } from "@/lib/api";
import { motion } from "framer-motion";
import { useState } from "react";
import { toast } from "sonner";

export default function CompressImagePage() {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showConfetti, setShowConfetti] = useState(false);
  const [quality, setQuality] = useState(85); // Default quality is 85%

  const handleFilesSelected = (selectedFiles: File[]) => {
    // With replaceExisting=true, we'll always get just the latest file
    setFiles(selectedFiles);
  };

  const handleCompress = async () => {
    if (files.length === 0) {
      toast.error("Please select an image file to compress");
      return;
    }

    setIsProcessing(true);
    setProgress(10);

    try {
      // Create a FormData object to send the file
      const formData = new FormData();
      formData.append("file", files[0]);
      formData.append("quality", quality.toString());

      // Simulate progress
      const progressInterval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 80) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 5;
        });
      }, 300);

      setProgress(30);

      // Use the API utility instead of direct fetch
      const response = await apiPost("compress-image", formData);

      setProgress(90);

      // Use the utility function to handle the download
      await downloadFromResponse(response, `compressed_${files[0].name}`);

      setProgress(100);
      setShowConfetti(true);
      toast.success("Image compressed successfully");
    } catch (error) {
      console.error("Error compressing image:", error);
      toast.error("Failed to compress image. Please try again.");
    } finally {
      setTimeout(() => {
        setIsProcessing(false);
        setProgress(0);
      }, 500);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 400, damping: 30 },
    },
  };

  return (
    <MainLayout>
      {showConfetti && <SuccessConfetti />}

      <motion.div
        className="max-w-3xl mx-auto space-y-8"
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        <motion.div className="text-center space-y-2" variants={itemVariants}>
          <h1 className="text-3xl font-bold tracking-tight font-display">
            Image Compressor
          </h1>
          <p className="text-muted-foreground">
            Reduce the file size of your images without losing quality
          </p>
        </motion.div>

        <motion.div className="space-y-6" variants={itemVariants}>
          <EnhancedFileUpload
            acceptedFileTypes={{
              "image/jpeg": [".jpg", ".jpeg"],
              "image/png": [".png"],
              "image/webp": [".webp"],
              "image/gif": [".gif"],
              "image/bmp": [".bmp"],
              "image/tiff": [".tiff", ".tif"],
            }}
            maxFiles={1}
            onFilesSelected={handleFilesSelected}
            isProcessing={isProcessing}
            processingProgress={progress}
            replaceExisting={true}
          />

          {files.length > 0 && (
            <div className="bg-muted/30 p-4 rounded-lg space-y-4">
              <h3 className="font-medium font-display">Compression Options</h3>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Quality: {quality}%</span>
                  <span className="text-xs text-muted-foreground">
                    {quality < 50
                      ? "Low quality, smaller file size"
                      : quality < 80
                      ? "Balanced quality and file size"
                      : "High quality, larger file size"}
                  </span>
                </div>
                <Slider
                  value={[quality]}
                  min={1}
                  max={100}
                  step={1}
                  onValueChange={(value) => setQuality(value[0])}
                  disabled={isProcessing}
                />
              </div>
            </div>
          )}

          <div className="flex justify-end">
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                onClick={handleCompress}
                disabled={files.length === 0 || isProcessing}
                className="px-8"
              >
                {isProcessing ? "Compressing..." : "Compress Image"}
              </Button>
            </motion.div>
          </div>
        </motion.div>

        <motion.div
          className="bg-muted/30 p-6 rounded-lg space-y-4"
          variants={itemVariants}
        >
          <h2 className="text-xl font-bold font-display">
            About Image Compression
          </h2>
          <div className="space-y-2 text-sm text-muted-foreground">
            <p>
              Our image compressor uses advanced algorithms to reduce the file
              size of your images while maintaining visual quality. This is
              useful for:
            </p>
            <ul className="list-disc list-inside space-y-1">
              <li>Reducing website loading times</li>
              <li>Saving storage space</li>
              <li>Sharing images via email or messaging apps</li>
              <li>Uploading to social media platforms</li>
            </ul>
            <p>
              The quality slider lets you control the balance between file size
              and image quality. Higher values preserve more details but result
              in larger files.
            </p>
          </div>
        </motion.div>
      </motion.div>
    </MainLayout>
  );
}
