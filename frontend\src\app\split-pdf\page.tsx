"use client";

import { EnhancedFileUpload } from "@/components/EnhancedFileUpload";
import { MainLayout } from "@/components/MainLayout";
import { SuccessConfetti } from "@/components/SuccessConfetti";
import { Button } from "@/components/ui/button";
import { apiPost, downloadFromResponse } from "@/lib/api";
import { motion } from "framer-motion";
import { useState } from "react";
import { toast } from "sonner";

export default function SplitPDFPage() {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showConfetti, setShowConfetti] = useState(false);
  const [splitOptions, setSplitOptions] = useState<"all" | "range">("all");
  const [pageRange, setPageRange] = useState("");

  const handleFilesSelected = (selectedFiles: File[]) => {
    // With replaceExisting=true, we'll always get just the latest file
    setFiles(selectedFiles);
  };

  const handleSplitPDF = async () => {
    if (files.length === 0) {
      toast.error("Please select a PDF file to split");
      return;
    }

    setIsProcessing(true);
    setProgress(0);

    try {
      // Create a FormData object to send the file and parameters
      const formData = new FormData();
      formData.append("file", files[0]);
      formData.append("split_type", splitOptions);

      if (splitOptions === "range") {
        if (!pageRange.trim()) {
          toast.error("Please enter page ranges");
          setIsProcessing(false);
          return;
        }
        formData.append("page_ranges", pageRange.trim());
      }

      // Set up progress tracking
      const progressInterval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 90) {
            return 90; // Cap at 90% until we get the response
          }
          return prev + 5;
        });
      }, 200);

      // Make the API call using the API utility
      const response = await apiPost("split-pdf", formData);

      clearInterval(progressInterval);

      // Set progress to 100% when done
      setProgress(100);

      // Use the utility function to handle the download
      await downloadFromResponse(response, "split_pdf.zip");

      // Show success message and confetti
      setShowConfetti(true);
      toast.success("PDF split successfully!");
    } catch (error) {
      console.error("Error splitting PDF:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to split PDF"
      );
    } finally {
      setIsProcessing(false);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.08,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 10, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 500, damping: 25 },
    },
  };

  return (
    <MainLayout>
      <SuccessConfetti
        show={showConfetti}
        duration={3000}
        onComplete={() => setShowConfetti(false)}
      />

      <motion.div
        className="max-w-3xl mx-auto"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div className="mb-8 space-y-2" variants={itemVariants}>
          <h1 className="text-3xl font-bold">Split PDF</h1>
          <p className="text-muted-foreground">
            Divide your PDF into separate pages or extract specific pages.
            Upload your PDF file, select your splitting options, and download
            the results.
          </p>
        </motion.div>

        <motion.div className="space-y-6" variants={itemVariants}>
          <EnhancedFileUpload
            acceptedFileTypes={{ "application/pdf": [".pdf"] }}
            maxFiles={1}
            onFilesSelected={handleFilesSelected}
            isProcessing={isProcessing}
            processingProgress={progress}
            replaceExisting={true}
          />

          {files.length > 0 && (
            <div className="bg-muted/30 p-4 rounded-lg space-y-4">
              <h3 className="font-medium font-display">Split Options</h3>

              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="split-all"
                    name="split-option"
                    checked={splitOptions === "all"}
                    onChange={() => setSplitOptions("all")}
                    className="h-4 w-4 text-primary"
                  />
                  <label htmlFor="split-all">
                    Split all pages (one PDF per page)
                  </label>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="split-range"
                    name="split-option"
                    checked={splitOptions === "range"}
                    onChange={() => setSplitOptions("range")}
                    className="h-4 w-4 text-primary"
                  />
                  <label htmlFor="split-range">Extract specific pages</label>
                </div>

                {splitOptions === "range" && (
                  <div className="pl-6 pt-2">
                    <input
                      type="text"
                      placeholder="e.g., 1-3, 5, 7-9"
                      value={pageRange}
                      onChange={(e) => setPageRange(e.target.value)}
                      className="w-full p-2 border rounded-md text-sm"
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Enter page numbers and/or ranges separated by commas
                      (e.g., 1-3, 5, 7-9)
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {files.length > 0 && (
            <div className="flex justify-end">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  onClick={handleSplitPDF}
                  disabled={isProcessing}
                  className="px-8"
                >
                  {isProcessing ? "Processing..." : "Split PDF"}
                </Button>
              </motion.div>
            </div>
          )}
        </motion.div>

        <motion.div
          className="mt-12 bg-muted/50 p-6 rounded-lg"
          variants={itemVariants}
        >
          <h2 className="text-xl font-semibold mb-4 font-display">
            About PDF Splitting
          </h2>
          <div className="space-y-4 text-sm text-muted-foreground">
            <p>
              Our PDF splitting tool allows you to divide your PDF documents
              into individual pages or extract specific page ranges. This is
              useful when you only need certain pages from a large document or
              want to reorganize your PDF content.
            </p>
            <p>
              You can choose to split the entire document into individual pages,
              or extract specific pages by entering page numbers and ranges. The
              tool will create a zip file containing all the extracted PDFs,
              making it easy to download and organize your content.
            </p>
            <p>
              <strong>Note:</strong> Your files are processed securely on our
              servers and are not stored permanently. They are automatically
              deleted after processing.
            </p>
          </div>
        </motion.div>
      </motion.div>
    </MainLayout>
  );
}
