"use client";

import { MainLayout } from "@/components/MainLayout";
import Link from "next/link";

export default function PDFToolsPage() {
  const tools = [
    {
      title: "Merge PDF",
      description: "Combine multiple PDF files into a single document",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-1)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-6 w-6"
        >
          <path d="M8 3H5a2 2 0 0 0-2 2v14c0 1.1.9 2 2 2h14a2 2 0 0 0 2-2V8h-3"></path>
          <path d="M17 21v-8h8"></path>
          <path d="M17 13 7 3"></path>
        </svg>
      ),
      href: "/merge-pdf",
    },
    {
      title: "Split PDF",
      description:
        "Divide your PDF into separate pages or extract specific pages",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-4)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-6 w-6"
        >
          <path d="M21 11V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h6"></path>
          <path d="M16 16v6"></path>
          <path d="M8 8v8"></path>
          <path d="M12 16v2"></path>
          <path d="M22 22H10"></path>
        </svg>
      ),
      href: "/split-pdf",
    },
    {
      title: "Compress PDF",
      description: "Reduce the file size of your PDF documents",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-2)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-6 w-6"
        >
          <path d="M5 8V5c0-1 1-2 2-2h10c1 0 2 1 2 2v3"></path>
          <path d="M19 16v3c0 1-1 2-2 2H7c-1 0-2-1-2-2v-3"></path>
          <line x1="12" x2="12" y1="4" y2="20"></line>
          <polyline points="9 7 12 4 15 7"></polyline>
          <polyline points="15 17 12 20 9 17"></polyline>
        </svg>
      ),
      href: "/compress-pdf",
    },
    {
      title: "Rotate PDF",
      description:
        "Rotate pages in your PDF document to the correct orientation",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-5)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-6 w-6"
        >
          <path d="M21 2v6h-6"></path>
          <path d="M21 13a9 9 0 1 1-3-7.7L21 8"></path>
        </svg>
      ),
      href: "/rotate-pdf",
    },
    {
      title: "Word to PDF",
      description: "Convert Microsoft Word documents to PDF format",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-3)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-6 w-6"
        >
          <path d="M4 22h14a2 2 0 0 0 2-2V7.5L14.5 2H6a2 2 0 0 0-2 2v4"></path>
          <polyline points="14 2 14 8 20 8"></polyline>
          <path d="M2 15h10"></path>
          <path d="m9 18 3-3-3-3"></path>
        </svg>
      ),
      href: "/word-to-pdf",
    },
    {
      title: "PDF to Word",
      description: "Convert PDF documents to editable Word format",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-6)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-6 w-6"
        >
          <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
          <polyline points="14 2 14 8 20 8"></polyline>
          <path d="M10 13 8 17h2l1-2h2l1 2h2l-2-4"></path>
        </svg>
      ),
      href: "/pdf-to-word",
    },
  ];

  return (
    <MainLayout>
      <div className="space-y-6 max-w-4xl mx-auto">
        <div className="text-center space-y-2 mb-6 pt-2">
          <h1 className="text-4xl font-bold tracking-tight font-display">
            PDF Tools
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            No installation, no registration, 100% secure & free
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 max-w-4xl mx-auto">
          {tools.map((tool) => (
            <Link key={tool.href} href={tool.href} className="block">
              <div className="h-full p-6 rounded-lg border bg-card hover:bg-accent/50 transition-colors group">
                <div className="p-1 w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center mb-3 group-hover:bg-primary/20 transition-colors">
                  {tool.icon}
                </div>
                <h3 className="font-display text-base font-semibold mb-2">
                  {tool.title}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {tool.description}
                </p>
              </div>
            </Link>
          ))}
        </div>

        <div className="mt-8 text-center">
          <h2 className="text-xl font-bold mb-4 font-display">How It Works</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto">
            <div className="flex flex-col items-center">
              <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mb-2">
                <span className="text-sm font-bold">1</span>
              </div>
              <h3 className="text-base font-medium mb-1 font-display">
                Upload Files
              </h3>
              <p className="text-sm text-muted-foreground text-center">
                Drag and drop your files or click to select them from your
                device
              </p>
            </div>
            <div className="flex flex-col items-center">
              <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mb-2">
                <span className="text-sm font-bold">2</span>
              </div>
              <h3 className="text-base font-medium mb-1 font-display">
                Process
              </h3>
              <p className="text-sm text-muted-foreground text-center">
                Our system will process your files quickly and efficiently
              </p>
            </div>
            <div className="flex flex-col items-center">
              <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mb-2">
                <span className="text-sm font-bold">3</span>
              </div>
              <h3 className="text-base font-medium mb-1 font-display">
                Download
              </h3>
              <p className="text-sm text-muted-foreground text-center">
                Download your processed files immediately to your device
              </p>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
