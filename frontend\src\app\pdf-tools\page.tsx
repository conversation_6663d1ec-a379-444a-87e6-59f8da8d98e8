"use client";

import { MainLayout } from "@/components/MainLayout";
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { motion } from "framer-motion";
import Link from "next/link";
import { useEffect, useState } from "react";

export default function PDFToolsPage() {
  const [activeToolIndex, setActiveToolIndex] = useState<number | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  const tools = [
    {
      title: "Merge PDF",
      description: "Combine multiple PDF files into a single document",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-1)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-6 w-6"
        >
          <path d="M8 3H5a2 2 0 0 0-2 2v14c0 1.1.9 2 2 2h14a2 2 0 0 0 2-2V8h-3"></path>
          <path d="M17 21v-8h8"></path>
          <path d="M17 13 7 3"></path>
        </svg>
      ),
      href: "/merge-pdf",
    },
    {
      title: "Split PDF",
      description:
        "Divide your PDF into separate pages or extract specific pages",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-4)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-6 w-6"
        >
          <path d="M21 11V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h6"></path>
          <path d="M16 16v6"></path>
          <path d="M8 8v8"></path>
          <path d="M12 16v2"></path>
          <path d="M22 22H10"></path>
        </svg>
      ),
      href: "/split-pdf",
    },
    {
      title: "Compress PDF",
      description: "Reduce the file size of your PDF documents",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-2)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-6 w-6"
        >
          <path d="M5 8V5c0-1 1-2 2-2h10c1 0 2 1 2 2v3"></path>
          <path d="M19 16v3c0 1-1 2-2 2H7c-1 0-2-1-2-2v-3"></path>
          <line x1="12" x2="12" y1="4" y2="20"></line>
          <polyline points="9 7 12 4 15 7"></polyline>
          <polyline points="15 17 12 20 9 17"></polyline>
        </svg>
      ),
      href: "/compress-pdf",
    },
    {
      title: "Rotate PDF",
      description:
        "Rotate pages in your PDF document to the correct orientation",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-5)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-6 w-6"
        >
          <path d="M21 2v6h-6"></path>
          <path d="M21 13a9 9 0 1 1-3-7.7L21 8"></path>
        </svg>
      ),
      href: "/rotate-pdf",
    },
    {
      title: "Word to PDF",
      description: "Convert Microsoft Word documents to PDF format",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-3)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-6 w-6"
        >
          <path d="M4 22h14a2 2 0 0 0 2-2V7.5L14.5 2H6a2 2 0 0 0-2 2v4"></path>
          <polyline points="14 2 14 8 20 8"></polyline>
          <path d="M2 15h10"></path>
          <path d="m9 18 3-3-3-3"></path>
        </svg>
      ),
      href: "/word-to-pdf",
    },
    {
      title: "PDF to Word",
      description: "Convert PDF documents to editable Word format",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-6)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-6 w-6"
        >
          <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
          <polyline points="14 2 14 8 20 8"></polyline>
          <path d="M10 13 8 17h2l1-2h2l1 2h2l-2-4"></path>
        </svg>
      ),
      href: "/pdf-to-word",
    },
  ];

  // Optimized animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.08,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 10, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 500, damping: 25 },
    },
  };

  const stepsVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const stepItemVariants = {
    hidden: { scale: 0.9, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: { type: "spring", stiffness: 500, damping: 25 },
    },
  };

  const titleVariants = {
    hidden: { opacity: 0, y: -10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 500,
        damping: 25,
        delay: 0.1,
      },
    },
  };

  // Background gradient animation (simplified)
  const gradientVariants = {
    initial: {
      opacity: 0,
    },
    animate: {
      opacity: 0.3,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <MainLayout>
      <motion.div
        className="absolute inset-0 -z-10 opacity-20 overflow-hidden"
        variants={gradientVariants}
        initial="initial"
        animate="animate"
        style={{
          background:
            "linear-gradient(120deg, rgba(var(--primary-rgb), 0.05) 0%, rgba(var(--secondary-rgb), 0.05) 50%, rgba(var(--primary-rgb), 0.05) 100%)",
        }}
      />

      <motion.div
        className="space-y-6 max-w-4xl mx-auto"
        initial="hidden"
        animate={isLoaded ? "visible" : "hidden"}
        variants={containerVariants}
      >
        <motion.div
          className="text-center space-y-2 mb-6 pt-2"
          variants={titleVariants}
        >
          <motion.div
            className="inline-block relative"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.1, duration: 0.3 }}
          >
            <motion.h1
              className="text-4xl font-bold tracking-tight font-display relative"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.2 }}
            >
              PDF Tools
            </motion.h1>
          </motion.div>
          <motion.p
            className="text-lg text-muted-foreground max-w-2xl mx-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.3 }}
          >
            No installation, no registration, 100% secure & free
          </motion.p>
        </motion.div>

        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 max-w-4xl mx-auto"
          variants={containerVariants}
        >
          {tools.map((tool, index) => (
            <motion.div
              key={tool.href}
              variants={itemVariants}
              whileHover={{
                scale: 1.03,
                boxShadow:
                  "0 10px 25px -5px rgba(var(--primary-rgb), 0.1), 0 8px 10px -6px rgba(var(--primary-rgb), 0.1)",
                transition: { duration: 0.2 },
              }}
              onHoverStart={() => setActiveToolIndex(index)}
              onHoverEnd={() => setActiveToolIndex(null)}
              className="flex"
            >
              <Link href={tool.href} className="block w-full">
                <Card className="h-full transition-all relative overflow-hidden flex flex-col backdrop-blur-sm bg-card/95 border-primary/10">
                  {activeToolIndex === index && (
                    <motion.div
                      className="absolute inset-0 bg-primary/5"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      layoutId="highlight"
                    />
                  )}
                  <CardHeader className="flex-grow">
                    <motion.div
                      className="p-1 w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center mb-2"
                      whileHover={{ scale: 1.05 }}
                      transition={{ duration: 0.2 }}
                    >
                      {tool.icon}
                    </motion.div>
                    <CardTitle className="font-display text-base mb-1">
                      {tool.title}
                    </CardTitle>
                    <CardDescription className="min-h-[40px] text-xs">
                      {tool.description}
                    </CardDescription>
                  </CardHeader>
                </Card>
              </Link>
            </motion.div>
          ))}
        </motion.div>

        <motion.div className="mt-6 text-center" variants={stepsVariants}>
          <motion.h2
            className="text-xl font-bold mb-4 font-display"
            variants={itemVariants}
          >
            How It Works
          </motion.h2>
          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto"
            variants={containerVariants}
          >
            {[
              {
                step: 1,
                title: "Upload Files",
                description:
                  "Drag and drop your files or click to select them from your device",
              },
              {
                step: 2,
                title: "Process",
                description:
                  "Our system will process your files quickly and efficiently",
              },
              {
                step: 3,
                title: "Download",
                description:
                  "Download your processed files immediately to your device",
              },
            ].map((step) => (
              <motion.div
                key={step.step}
                className="flex flex-col items-center"
                variants={stepItemVariants}
                whileHover={{ y: -3 }}
              >
                <motion.div
                  className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mb-2 relative"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <motion.span className="text-sm font-bold">
                    {step.step}
                  </motion.span>
                </motion.div>
                <h3 className="text-base font-medium mb-1 font-display">
                  {step.title}
                </h3>
                <p className="text-sm text-muted-foreground text-center">
                  {step.description}
                </p>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </motion.div>
    </MainLayout>
  );
}
