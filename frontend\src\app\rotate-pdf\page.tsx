"use client";

import { EnhancedFileUpload } from "@/components/EnhancedFileUpload";
import { MainLayout } from "@/components/MainLayout";
import { SuccessConfetti } from "@/components/SuccessConfetti";
import { Button } from "@/components/ui/button";
import { apiPost, downloadFromResponse } from "@/lib/api";
import { motion } from "framer-motion";
import { useState } from "react";
import { toast } from "sonner";

export default function RotatePDFPage() {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showConfetti, setShowConfetti] = useState(false);
  const [rotationAngle, setRotationAngle] = useState<90 | 180 | 270>(90);

  const handleFilesSelected = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
  };

  const handleRotatePDF = async () => {
    if (files.length === 0) {
      toast.error("Please select at least one PDF file");
      return;
    }

    setIsProcessing(true);
    setProgress(0);

    try {
      // Create a FormData object to send the files and rotation angle
      const formData = new FormData();
      files.forEach((file) => {
        formData.append("files", file);
      });
      formData.append("rotation_angle", rotationAngle.toString());

      // Set up progress tracking
      const progressInterval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 90) {
            return 90; // Cap at 90% until we get the response
          }
          return prev + 5;
        });
      }, 200);

      // Make the API call using the API utility
      const response = await apiPost("rotate-pdf", formData);

      clearInterval(progressInterval);

      // Set progress to 100% when done
      setProgress(100);

      // Use the utility function to handle the download
      const defaultFilename =
        files.length > 1 ? "rotated_pdfs.zip" : `rotated_${files[0].name}`;
      await downloadFromResponse(response, defaultFilename);

      // Show success message and confetti
      setShowConfetti(true);
      toast.success("PDF rotated successfully!");
    } catch (error) {
      console.error("Error rotating PDF:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to rotate PDF"
      );
    } finally {
      setIsProcessing(false);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.08,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 10, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 500, damping: 25 },
    },
  };

  return (
    <MainLayout>
      <SuccessConfetti
        show={showConfetti}
        duration={3000}
        onComplete={() => setShowConfetti(false)}
      />

      <motion.div
        className="max-w-3xl mx-auto"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div className="mb-8 space-y-2" variants={itemVariants}>
          <h1 className="text-3xl font-bold">Rotate PDF</h1>
          <p className="text-muted-foreground">
            Rotate pages in your PDF document to the correct orientation. Upload
            your PDF file, select rotation angle, and download the result.
          </p>
        </motion.div>

        <motion.div className="space-y-6" variants={itemVariants}>
          <EnhancedFileUpload
            acceptedFileTypes={{ "application/pdf": [".pdf"] }}
            maxFiles={5}
            onFilesSelected={handleFilesSelected}
            isProcessing={isProcessing}
            processingProgress={progress}
          />

          {files.length > 0 && (
            <div className="bg-muted/30 p-4 rounded-lg space-y-4">
              <h3 className="font-medium font-display">Rotation Options</h3>

              <div className="grid grid-cols-3 gap-4">
                <div
                  className={`border rounded-lg p-4 text-center cursor-pointer transition-all ${
                    rotationAngle === 90
                      ? "border-primary bg-primary/5"
                      : "hover:border-primary/50"
                  }`}
                  onClick={() => setRotationAngle(90)}
                >
                  <div className="flex justify-center mb-2">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="transform rotate-90"
                    >
                      <path d="M12 2v8"></path>
                      <path d="m16 6-4 4-4-4"></path>
                      <path d="M22 17a5 5 0 0 1-10 0c0-2.76 2.24-5 5-5 .71 0 1.39.15 2 .42V10"></path>
                      <path d="M2 12a5 5 0 0 1 10 0c0 2.76-2.24 5-5 5-.71 0-1.39-.15-2-.42"></path>
                    </svg>
                  </div>
                  <span className="text-sm font-medium">
                    Rotate 90° Clockwise
                  </span>
                </div>

                <div
                  className={`border rounded-lg p-4 text-center cursor-pointer transition-all ${
                    rotationAngle === 180
                      ? "border-primary bg-primary/5"
                      : "hover:border-primary/50"
                  }`}
                  onClick={() => setRotationAngle(180)}
                >
                  <div className="flex justify-center mb-2">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="transform rotate-180"
                    >
                      <path d="M12 2v8"></path>
                      <path d="m16 6-4 4-4-4"></path>
                      <path d="M22 17a5 5 0 0 1-10 0c0-2.76 2.24-5 5-5 .71 0 1.39.15 2 .42V10"></path>
                      <path d="M2 12a5 5 0 0 1 10 0c0 2.76-2.24 5-5 5-.71 0-1.39-.15-2-.42"></path>
                    </svg>
                  </div>
                  <span className="text-sm font-medium">Rotate 180°</span>
                </div>

                <div
                  className={`border rounded-lg p-4 text-center cursor-pointer transition-all ${
                    rotationAngle === 270
                      ? "border-primary bg-primary/5"
                      : "hover:border-primary/50"
                  }`}
                  onClick={() => setRotationAngle(270)}
                >
                  <div className="flex justify-center mb-2">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="transform -rotate-90"
                    >
                      <path d="M12 2v8"></path>
                      <path d="m16 6-4 4-4-4"></path>
                      <path d="M22 17a5 5 0 0 1-10 0c0-2.76 2.24-5 5-5 .71 0 1.39.15 2 .42V10"></path>
                      <path d="M2 12a5 5 0 0 1 10 0c0 2.76-2.24 5-5 5-.71 0-1.39-.15-2-.42"></path>
                    </svg>
                  </div>
                  <span className="text-sm font-medium">
                    Rotate 90° Counter-Clockwise
                  </span>
                </div>
              </div>
            </div>
          )}

          {files.length > 0 && (
            <div className="flex justify-end">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  onClick={handleRotatePDF}
                  disabled={isProcessing}
                  className="px-8"
                >
                  {isProcessing ? "Processing..." : "Rotate PDF"}
                </Button>
              </motion.div>
            </div>
          )}
        </motion.div>

        <motion.div
          className="mt-12 bg-muted/50 p-6 rounded-lg"
          variants={itemVariants}
        >
          <h2 className="text-xl font-semibold mb-4 font-display">
            About PDF Rotation
          </h2>
          <div className="space-y-4 text-sm text-muted-foreground">
            <p>
              Our PDF rotation tool allows you to change the orientation of
              pages in your PDF documents. This is useful when you have scanned
              documents or PDFs with pages that are upside down or sideways.
            </p>
            <p>
              You can choose to rotate pages by 90°, 180°, or 270° degrees. The
              tool supports batch processing, allowing you to rotate multiple
              PDF files at once with the same rotation angle. For multiple
              files, the results will be provided as a zip file containing all
              the rotated PDFs.
            </p>
            <p>
              <strong>Note:</strong> Your files are processed securely on our
              servers and are not stored permanently. They are automatically
              deleted after processing.
            </p>
          </div>
        </motion.div>
      </motion.div>
    </MainLayout>
  );
}
