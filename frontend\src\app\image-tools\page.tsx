import { MainLayout } from "@/components/MainLayout";
import Link from "next/link";

export default function ImageToolsPage() {
  const tools = [
    {
      title: "Image Compressor",
      description: "Reduce the file size of your images without losing quality",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-1)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-6 w-6"
        >
          <rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect>
          <circle cx="9" cy="9" r="2"></circle>
          <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"></path>
        </svg>
      ),
      href: "/compress-image",
    },
    {
      title: "Image Converter",
      description: "Convert images between different formats (PNG, JPG, WebP)",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-2)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-6 w-6"
        >
          <path d="M21 12V7H5a2 2 0 0 1 0-4h14v4"></path>
          <path d="M3 12v5h16a2 2 0 0 1 0 4H3v-4"></path>
        </svg>
      ),
      href: "/convert-image",
    },
    {
      title: "Image Resizer",
      description: "Resize your images to specific dimensions",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-3)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-6 w-6"
        >
          <path d="M15 3h6v6"></path>
          <path d="M9 21H3v-6"></path>
          <path d="m21 3-9 9"></path>
          <path d="m3 21 9-9"></path>
        </svg>
      ),
      href: "/resize-image",
    },
    {
      title: "Crop Image",
      description: "Crop your images to specific dimensions or aspect ratios",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-4)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-6 w-6"
        >
          <path d="M6 2v14a2 2 0 0 0 2 2h14"></path>
          <path d="M18 22V8a2 2 0 0 0-2-2H2"></path>
        </svg>
      ),
      href: "/crop-image",
    },
    {
      title: "Image Upscaler",
      description: "Increase resolution and enhance image quality",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-5)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-6 w-6"
        >
          <path d="m7 21-4.3-4.3c-1-1-1-2.5 0-3.4l9.6-9.6c1-1 2.5-1 3.4 0l5.6 5.6c1 1 1 2.5 0 3.4L13 21"></path>
          <path d="M22 21H7"></path>
          <path d="m5 11 9 9"></path>
        </svg>
      ),
      href: "/upscale-image",
    },
    {
      title: "Image to PDF",
      description: "Convert images to PDF documents with custom settings",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-6)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-6 w-6"
        >
          <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7l-5-5Z"></path>
          <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
          <path d="M8 10v8"></path>
          <path d="M8 10a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"></path>
          <path d="M12 18a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"></path>
          <path d="M16 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"></path>
        </svg>
      ),
      href: "/image-to-pdf",
    },
  ];

  return (
    <MainLayout>
      <div className="space-y-6 max-w-4xl mx-auto">
        <div className="text-center space-y-2 mb-6 pt-2">
          <h1 className="text-4xl font-bold tracking-tight font-display">
            Image Tools
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            No installation, no registration, 100% secure & free
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 max-w-4xl mx-auto">
          {tools.map((tool) => (
            <Link key={tool.href} href={tool.href} className="block">
              <div className="h-full p-6 rounded-lg border bg-card hover:bg-accent/50 transition-colors group">
                <div className="p-1 w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center mb-3 group-hover:bg-primary/20 transition-colors">
                  {tool.icon}
                </div>
                <h3 className="font-display text-base font-semibold mb-2">
                  {tool.title}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {tool.description}
                </p>
              </div>
            </Link>
          ))}
        </div>

        <div className="mt-8 text-center">
          <h2 className="text-xl font-bold mb-4 font-display">How It Works</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto">
            <div className="flex flex-col items-center">
              <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mb-2">
                <span className="text-sm font-bold">1</span>
              </div>
              <h3 className="text-base font-medium mb-1 font-display">
                Upload Files
              </h3>
              <p className="text-sm text-muted-foreground text-center">
                Drag and drop your images or click to select them from your
                device
              </p>
            </div>
            <div className="flex flex-col items-center">
              <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mb-2">
                <span className="text-sm font-bold">2</span>
              </div>
              <h3 className="text-base font-medium mb-1 font-display">
                Process
              </h3>
              <p className="text-sm text-muted-foreground text-center">
                Our system will process your images quickly and efficiently
              </p>
            </div>
            <div className="flex flex-col items-center">
              <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mb-2">
                <span className="text-sm font-bold">3</span>
              </div>
              <h3 className="text-base font-medium mb-1 font-display">
                Download
              </h3>
              <p className="text-sm text-muted-foreground text-center">
                Download your processed images immediately to your device
              </p>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
