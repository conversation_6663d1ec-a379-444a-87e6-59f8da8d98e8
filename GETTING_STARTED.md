# Getting Started with FreeFox

This guide will help you get the FreeFox application up and running on your local machine.

## Quick Start

1. Run the setup script to install all dependencies:

```
setup.bat
```

2. Run the application:

```
run_app.bat
```

3. Open your browser and navigate to:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000

## Manual Setup

If you prefer to set up the application manually, follow these steps:

### Frontend Setup

1. Navigate to the frontend directory:

```
cd frontend
```

2. Install dependencies:

```
npm install --legacy-peer-deps
```

3. Start the development server:

```
npm run dev
```

### Backend Setup

1. Navigate to the backend directory:

```
cd backend
```

2. Install dependencies:

```
pip install -r requirements.txt
```

3. Start the backend server:

```
python run.py
```

## Project Structure

```
freefox/
├── backend/                # Python FastAPI backend
│   ├── app/                # Application code
│   │   ├── routes/         # API routes
│   │   ├── services/       # Business logic
│   │   ├── utils/          # Utility functions
│   │   └── main.py         # Main application entry point
│   ├── requirements.txt    # Python dependencies
│   └── run.py              # Script to run the backend server
├── frontend/               # Next.js frontend
│   ├── src/                # Source code
│   │   ├── app/            # Next.js app directory
│   │   ├── components/     # React components
│   │   └── lib/            # Utility functions
│   ├── public/             # Static files
│   └── package.json        # Node.js dependencies
├── docs/                   # Documentation
│   ├── architecture.md     # Architecture documentation
│   ├── deployment.md       # Deployment guide
│   └── user_guide.md       # User guide
├── README.md               # Project overview
├── setup.bat               # Setup script
└── run_app.bat             # Script to run both frontend and backend
```

## Available Tools

The application provides the following tools:

1. **Merge PDF**: Combine multiple PDF files into a single document
2. **Compress PDF**: Reduce the file size of your PDF documents
3. **Word to PDF**: Convert Microsoft Word documents to PDF format

## Development

### Frontend Development

The frontend is built with Next.js and uses the following technologies:

- Next.js 14
- TypeScript
- Tailwind CSS
- Shadcn UI
- React Dropzone

To add a new component:

```
cd frontend
npx shadcn@latest add [component-name]
```

### Backend Development

The backend is built with FastAPI and uses the following technologies:

- FastAPI
- PyPDF2
- ReportLab

To add a new endpoint, create a new route in the `backend/app/routes` directory.

## Troubleshooting

### Common Issues

1. **Port conflicts**: Make sure ports 3000 and 8000 are available
2. **Dependency issues**: Run the setup script again or install dependencies manually
3. **File permissions**: Make sure you have write permissions in the project directories

### Getting Help

If you encounter any issues, please check the documentation in the `docs` directory or open an issue on the project repository.
