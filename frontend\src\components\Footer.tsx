import { AnimatePresence, motion } from "framer-motion";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";
import { FAQ } from "./FAQ";

export function Footer() {
  const [showFAQ, setShowFAQ] = useState(false);
  const faqRef = useRef<HTMLDivElement>(null);

  // Scroll to FAQ section when it's opened
  useEffect(() => {
    if (showFAQ && faqRef.current) {
      // Use a small timeout to ensure the FAQ section is rendered before scrolling
      const timeoutId = setTimeout(() => {
        // Calculate position to scroll to (a bit above the FAQ section to show more of it)
        const faqElement = faqRef.current;
        const faqRect = faqElement.getBoundingClientRect();
        const scrollTop =
          window.pageYOffset || document.documentElement.scrollTop;
        const targetPosition = scrollTop + faqRect.top - 100; // 100px above the FAQ section

        // Scroll to the calculated position
        window.scrollTo({
          top: targetPosition,
          behavior: "smooth",
        });
      }, 150); // Slightly longer timeout to ensure animation has started

      return () => clearTimeout(timeoutId);
    }
  }, [showFAQ]);

  return (
    <footer className="border-t mt-auto bg-background">
      <div className="container mx-auto px-4 py-4">
        {/* FAQ Section */}
        <AnimatePresence>
          {showFAQ && (
            <motion.div
              ref={faqRef}
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="overflow-hidden mb-8"
            >
              <FAQ onCollapse={() => setShowFAQ(false)} />
            </motion.div>
          )}
        </AnimatePresence>

        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="mb-4 md:mb-0">
            <p className="text-sm text-muted-foreground">
              © {new Date().getFullYear()} FreeFox. All rights reserved.
            </p>
          </div>
          <div className="flex flex-wrap justify-center md:justify-end gap-x-4 gap-y-2 items-center">
            <button
              onClick={() => setShowFAQ(!showFAQ)}
              className="text-sm font-medium text-muted-foreground hover:text-primary transition-colors flex items-center gap-1"
              aria-expanded={showFAQ}
              aria-controls="faq-section"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className={`h-4 w-4 transition-transform ${
                  showFAQ ? "rotate-180" : ""
                }`}
              >
                <path d="M12 19V5" />
                <path d="m5 12 7-7 7 7" />
              </svg>
              {showFAQ ? "Hide FAQ" : "FAQ"}
            </button>
            <span className="hidden md:inline text-muted-foreground/30">|</span>
            <Link
              href="/pdf-tools"
              className="text-sm text-muted-foreground hover:text-primary transition-colors"
            >
              PDF Tools
            </Link>
            <Link
              href="/image-tools"
              className="text-sm text-muted-foreground hover:text-primary transition-colors"
            >
              Image Tools
            </Link>
            <span className="hidden md:inline text-muted-foreground/30">|</span>
            <Link
              href="/terms"
              className="text-sm text-muted-foreground hover:text-primary transition-colors"
            >
              Terms & Conditions
            </Link>
            <Link
              href="/privacy"
              className="text-sm text-muted-foreground hover:text-primary transition-colors"
            >
              Privacy Policy
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
