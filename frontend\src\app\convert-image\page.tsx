"use client";

import { EnhancedFileUpload } from "@/components/EnhancedFileUpload";
import { MainLayout } from "@/components/MainLayout";
import { SuccessConfetti } from "@/components/SuccessConfetti";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { apiPost, downloadFromResponse } from "@/lib/api";
import { motion } from "framer-motion";
import { useState } from "react";
import { toast } from "sonner";

export default function ConvertImagePage() {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showConfetti, setShowConfetti] = useState(false);
  const [targetFormat, setTargetFormat] = useState("png");

  const handleFilesSelected = (selectedFiles: File[]) => {
    // With replaceExisting=true, we'll always get just the latest file
    setFiles(selectedFiles);
  };

  const handleConvert = async () => {
    if (files.length === 0) {
      toast.error("Please select an image file to convert");
      return;
    }

    setIsProcessing(true);
    setProgress(10);

    try {
      // Create a FormData object to send the file
      const formData = new FormData();
      formData.append("file", files[0]);
      formData.append("target_format", targetFormat);

      // Simulate progress
      const progressInterval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 80) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 5;
        });
      }, 300);

      setProgress(30);

      // Use the API utility instead of direct fetch
      const response = await apiPost("convert-image", formData);

      setProgress(90);

      // Get the original filename without extension
      const originalName =
        files[0].name.substring(0, files[0].name.lastIndexOf(".")) ||
        files[0].name;

      // Use the utility function to handle the download
      await downloadFromResponse(response, `${originalName}.${targetFormat}`);

      setProgress(100);
      setShowConfetti(true);
      toast.success(
        `Image converted to ${targetFormat.toUpperCase()} successfully`
      );
    } catch (error) {
      console.error("Error converting image:", error);
      toast.error("Failed to convert image. Please try again.");
    } finally {
      setTimeout(() => {
        setIsProcessing(false);
        setProgress(0);
      }, 500);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 400, damping: 30 },
    },
  };

  return (
    <MainLayout>
      <SuccessConfetti
        show={showConfetti}
        duration={3000}
        onComplete={() => setShowConfetti(false)}
      />

      <motion.div
        className="max-w-3xl mx-auto space-y-8"
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        <motion.div className="text-center space-y-2" variants={itemVariants}>
          <h1 className="text-3xl font-bold tracking-tight font-display">
            Image Converter
          </h1>
          <p className="text-muted-foreground">
            Convert your images between different formats
          </p>
        </motion.div>

        <motion.div className="space-y-6" variants={itemVariants}>
          <EnhancedFileUpload
            acceptedFileTypes={{
              "image/jpeg": [".jpg", ".jpeg"],
              "image/png": [".png"],
              "image/webp": [".webp"],
              "image/gif": [".gif"],
              "image/bmp": [".bmp"],
              "image/tiff": [".tiff", ".tif"],
            }}
            maxFiles={1}
            onFilesSelected={handleFilesSelected}
            isProcessing={isProcessing}
            processingProgress={progress}
            replaceExisting={true}
          />

          {files.length > 0 && (
            <div className="bg-muted/30 p-4 rounded-lg space-y-4">
              <h3 className="font-medium font-display">Conversion Options</h3>
              <div className="space-y-2">
                <div className="flex flex-col space-y-1">
                  <label className="text-sm font-medium">Target Format</label>
                  <Select
                    value={targetFormat}
                    onValueChange={setTargetFormat}
                    disabled={isProcessing}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select format" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="jpg">JPG</SelectItem>
                      <SelectItem value="png">PNG</SelectItem>
                      <SelectItem value="webp">WebP</SelectItem>
                      <SelectItem value="gif">GIF</SelectItem>
                      <SelectItem value="bmp">BMP</SelectItem>
                      <SelectItem value="tiff">TIFF</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}

          {files.length > 0 && (
            <div className="flex justify-end">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  onClick={handleConvert}
                  disabled={isProcessing}
                  className="px-8"
                >
                  {isProcessing ? "Converting..." : "Convert Image"}
                </Button>
              </motion.div>
            </div>
          )}
        </motion.div>

        <motion.div
          className="bg-muted/30 p-6 rounded-lg space-y-4"
          variants={itemVariants}
        >
          <h2 className="text-xl font-bold font-display">
            About Image Conversion
          </h2>
          <div className="space-y-2 text-sm text-muted-foreground">
            <p>
              Our image converter allows you to convert images between different
              formats. Each format has its own advantages:
            </p>
            <ul className="list-disc list-inside space-y-1">
              <li>
                <strong>JPG</strong>: Best for photographs, smaller file size
                but lossy compression
              </li>
              <li>
                <strong>PNG</strong>: Supports transparency, lossless
                compression, good for graphics
              </li>
              <li>
                <strong>WebP</strong>: Modern format with excellent compression,
                supports transparency
              </li>
              <li>
                <strong>GIF</strong>: Supports animation, limited color palette
              </li>
              <li>
                <strong>BMP</strong>: Uncompressed format, large file size but
                no quality loss
              </li>
              <li>
                <strong>TIFF</strong>: High-quality format used in professional
                photography and printing
              </li>
            </ul>
            <p>
              Choose the format that best suits your needs based on quality,
              file size, and compatibility requirements.
            </p>
          </div>
        </motion.div>
      </motion.div>
    </MainLayout>
  );
}
