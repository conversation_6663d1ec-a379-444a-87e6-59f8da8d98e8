"use client";

import { EnhancedFileUpload } from "@/components/EnhancedFileUpload";
import { MainLayout } from "@/components/MainLayout";
import { SuccessConfetti } from "@/components/SuccessConfetti";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { apiPost, downloadFromResponse } from "@/lib/api";
import { motion } from "framer-motion";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";

export default function CropImagePage() {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showConfetti, setShowConfetti] = useState(false);
  const [left, setLeft] = useState(0);
  const [top, setTop] = useState(0);
  const [right, setRight] = useState(0);
  const [bottom, setBottom] = useState(0);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [imageDimensions, setImageDimensions] = useState({ width: 0, height: 0 });
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [cropBox, setCropBox] = useState({ x: 0, y: 0, width: 0, height: 0 });

  const handleFilesSelected = (selectedFiles: File[]) => {
    // With replaceExisting=true, we'll always get just the latest file
    setFiles(selectedFiles);
    
    if (selectedFiles.length > 0) {
      // Create a preview of the image
      const file = selectedFiles[0];
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          setImagePreview(e.target.result as string);
          
          // Get image dimensions
          const img = new Image();
          img.onload = () => {
            setImageDimensions({ width: img.width, height: img.height });
            // Initialize crop box to full image
            setCropBox({ x: 0, y: 0, width: img.width, height: img.height });
            setLeft(0);
            setTop(0);
            setRight(img.width);
            setBottom(img.height);
          };
          img.src = e.target.result as string;
        }
      };
      reader.readAsDataURL(file);
    } else {
      setImagePreview(null);
      setImageDimensions({ width: 0, height: 0 });
    }
  };

  // Draw the image and crop box on the canvas
  useEffect(() => {
    if (!imagePreview || !canvasRef.current) return;
    
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    const img = new Image();
    img.onload = () => {
      // Set canvas size to match image
      canvas.width = img.width;
      canvas.height = img.height;
      
      // Draw image
      ctx.drawImage(img, 0, 0);
      
      // Draw semi-transparent overlay
      ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      // Clear the crop area
      ctx.clearRect(cropBox.x, cropBox.y, cropBox.width, cropBox.height);
      
      // Draw crop box border
      ctx.strokeStyle = 'rgba(var(--primary-rgb), 1)';
      ctx.lineWidth = 2;
      ctx.strokeRect(cropBox.x, cropBox.y, cropBox.width, cropBox.height);
    };
    img.src = imagePreview;
  }, [imagePreview, cropBox]);

  // Update form inputs when crop box changes
  useEffect(() => {
    setLeft(Math.round(cropBox.x));
    setTop(Math.round(cropBox.y));
    setRight(Math.round(cropBox.x + cropBox.width));
    setBottom(Math.round(cropBox.y + cropBox.height));
  }, [cropBox]);

  // Handle mouse events for interactive cropping
  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current) return;
    
    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;
    
    const x = (e.clientX - rect.left) * scaleX;
    const y = (e.clientY - rect.top) * scaleY;
    
    setIsDragging(true);
    setDragStart({ x, y });
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDragging || !canvasRef.current) return;
    
    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;
    
    const x = (e.clientX - rect.left) * scaleX;
    const y = (e.clientY - rect.top) * scaleY;
    
    // Calculate new crop box
    const newX = Math.min(dragStart.x, x);
    const newY = Math.min(dragStart.y, y);
    const newWidth = Math.abs(x - dragStart.x);
    const newHeight = Math.abs(y - dragStart.y);
    
    // Update crop box
    setCropBox({
      x: newX,
      y: newY,
      width: newWidth,
      height: newHeight
    });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Update crop box when form inputs change
  const updateCropBoxFromInputs = () => {
    if (left >= 0 && top >= 0 && right > left && bottom > top) {
      setCropBox({
        x: left,
        y: top,
        width: right - left,
        height: bottom - top
      });
    }
  };

  const handleCrop = async () => {
    if (files.length === 0) {
      toast.error("Please select an image file to crop");
      return;
    }

    if (left < 0 || top < 0 || left >= right || top >= bottom) {
      toast.error("Invalid crop coordinates");
      return;
    }

    setIsProcessing(true);
    setProgress(10);

    try {
      // Create a FormData object to send the file
      const formData = new FormData();
      formData.append("file", files[0]);
      formData.append("left", left.toString());
      formData.append("top", top.toString());
      formData.append("right", right.toString());
      formData.append("bottom", bottom.toString());

      // Simulate progress
      const progressInterval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 80) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 5;
        });
      }, 300);

      setProgress(30);

      // Use the API utility instead of direct fetch
      const response = await apiPost("crop-image", formData);

      setProgress(90);

      // Use the utility function to handle the download
      await downloadFromResponse(response, `cropped_${files[0].name}`);

      setProgress(100);
      setShowConfetti(true);
      toast.success("Image cropped successfully");
    } catch (error) {
      console.error("Error cropping image:", error);
      toast.error("Failed to crop image. Please try again.");
    } finally {
      setTimeout(() => {
        setIsProcessing(false);
        setProgress(0);
      }, 500);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 400, damping: 30 },
    },
  };

  return (
    <MainLayout>
      {showConfetti && <SuccessConfetti />}

      <motion.div
        className="max-w-3xl mx-auto space-y-8"
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        <motion.div className="text-center space-y-2" variants={itemVariants}>
          <h1 className="text-3xl font-bold tracking-tight font-display">
            Crop Image
          </h1>
          <p className="text-muted-foreground">
            Crop your images to specific dimensions or aspect ratios
          </p>
        </motion.div>

        <motion.div className="space-y-6" variants={itemVariants}>
          <EnhancedFileUpload
            acceptedFileTypes={{
              "image/jpeg": [".jpg", ".jpeg"],
              "image/png": [".png"],
              "image/webp": [".webp"],
              "image/gif": [".gif"],
              "image/bmp": [".bmp"],
              "image/tiff": [".tiff", ".tif"],
            }}
            maxFiles={1}
            onFilesSelected={handleFilesSelected}
            isProcessing={isProcessing}
            processingProgress={progress}
            replaceExisting={true}
          />

          {imagePreview && (
            <div className="bg-muted/30 p-4 rounded-lg space-y-4">
              <h3 className="font-medium font-display">Crop Area</h3>
              
              <div className="relative overflow-hidden rounded-md border border-muted">
                <canvas
                  ref={canvasRef}
                  className="max-w-full h-auto cursor-crosshair"
                  onMouseDown={handleMouseDown}
                  onMouseMove={handleMouseMove}
                  onMouseUp={handleMouseUp}
                  onMouseLeave={handleMouseUp}
                />
              </div>
              
              <p className="text-xs text-muted-foreground">
                Click and drag on the image to select the crop area, or enter coordinates manually below.
              </p>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="left">Left (X)</Label>
                  <Input
                    id="left"
                    type="number"
                    min="0"
                    max={imageDimensions.width}
                    value={left}
                    onChange={(e) => {
                      setLeft(parseInt(e.target.value) || 0);
                      updateCropBoxFromInputs();
                    }}
                    disabled={isProcessing}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="top">Top (Y)</Label>
                  <Input
                    id="top"
                    type="number"
                    min="0"
                    max={imageDimensions.height}
                    value={top}
                    onChange={(e) => {
                      setTop(parseInt(e.target.value) || 0);
                      updateCropBoxFromInputs();
                    }}
                    disabled={isProcessing}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="right">Right (X)</Label>
                  <Input
                    id="right"
                    type="number"
                    min={left + 1}
                    max={imageDimensions.width}
                    value={right}
                    onChange={(e) => {
                      setRight(parseInt(e.target.value) || 0);
                      updateCropBoxFromInputs();
                    }}
                    disabled={isProcessing}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="bottom">Bottom (Y)</Label>
                  <Input
                    id="bottom"
                    type="number"
                    min={top + 1}
                    max={imageDimensions.height}
                    value={bottom}
                    onChange={(e) => {
                      setBottom(parseInt(e.target.value) || 0);
                      updateCropBoxFromInputs();
                    }}
                    disabled={isProcessing}
                  />
                </div>
              </div>
            </div>
          )}

          <div className="flex justify-end">
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                onClick={handleCrop}
                disabled={files.length === 0 || isProcessing}
                className="px-8"
              >
                {isProcessing ? "Cropping..." : "Crop Image"}
              </Button>
            </motion.div>
          </div>
        </motion.div>
      </motion.div>
    </MainLayout>
  );
}
