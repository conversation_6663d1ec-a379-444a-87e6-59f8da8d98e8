"use client";

import { EnhancedFileUpload } from "@/components/EnhancedFileUpload";
import { MainLayout } from "@/components/MainLayout";
import { SuccessConfetti } from "@/components/SuccessConfetti";
import { Button } from "@/components/ui/button";
import { apiPost, downloadFromResponse } from "@/lib/api"; // Assuming apiPost returns the Response object
import { motion } from "framer-motion";
import { useState } from "react";
import { toast } from "sonner";

// Helper function to format bytes
function formatBytes(bytes: number, decimals = 2) {
  if (!bytes || bytes === 0) return "0 Bytes";
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
}

interface CompressionStats {
  originalSize: number;
  compressedSize: number;
  ratio: string;
  originalFilename: string;
}

export default function CompressPDFPage() {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showConfetti, setShowConfetti] = useState(false);
  const [compressionStats, setCompressionStats] =
    useState<CompressionStats | null>(null);
  const [uploaderKey, setUploaderKey] = useState(0); // Used to reset EnhancedFileUpload

  const handleFilesSelected = (selectedFiles: File[]) => {
    // With replaceExisting=true, we'll always get just the latest file
    setFiles(selectedFiles);
    setCompressionStats(null); // Clear previous stats if a new file is selected
  };

  const handleCompress = async () => {
    if (files.length === 0) {
      toast.error("Please select a PDF file to compress");
      return;
    }

    setIsProcessing(true);
    setProgress(10);
    setCompressionStats(null);

    const currentFile = files[0];

    try {
      const formData = new FormData();
      formData.append("file", currentFile);

      // ... (simulated progress logic) ...
      setProgress(80);

      const response = await apiPost("compress-pdf", formData); // Ensure this returns the actual Response object

      setProgress(90);
      await downloadFromResponse(response, `compressed_${files[0].name}`);
      setProgress(100);

      console.log("Response object from apiPost:", response);
      console.log("Attempting to read compression headers...");

      // Log all headers to see what's available
      if (response && response.headers) {
        console.log("All Response Headers:");
        response.headers.forEach((value, key) => {
          console.log(`${key}: ${value}`);
        });
      } else {
        console.error(
          "Response object or response.headers is undefined. Cannot read headers."
        );
        toast.error("Error: Could not retrieve response headers for stats.");
        // Fall through to finally block
        return; // Exit if no headers object
      }

      const originalSizeHeader = response.headers.get("X-Original-Size");
      const compressedSizeHeader = response.headers.get("X-Compressed-Size");
      const ratioHeader = response.headers.get("X-Compression-Ratio");

      console.log("X-Original-Size from header:", originalSizeHeader);
      console.log("X-Compressed-Size from header:", compressedSizeHeader);
      console.log("X-Compression-Ratio from header:", ratioHeader);

      if (originalSizeHeader && compressedSizeHeader && ratioHeader) {
        console.log("All required headers found. Setting compression stats.");
        setCompressionStats({
          originalSize: parseInt(originalSizeHeader, 10),
          compressedSize: parseInt(compressedSizeHeader, 10),
          ratio: ratioHeader,
          originalFilename: currentFile.name,
        });
        toast.success(
          `"${currentFile.name}" compressed! Reduction: ${ratioHeader}`
        );
      } else {
        console.warn(
          "One or more compression headers were missing or null. Stats will not be displayed."
        );
        toast.success(
          `"${currentFile.name}" compressed and downloaded! (Stats unavailable)`
        );
      }

      setShowConfetti(true);
      setFiles([]); // Clear the file from parent state. This hides the "Compress PDF" button.
      // EnhancedFileUpload will be replaced by stats view if compressionStats is set.
    } catch (error) {
      console.error("Error compressing PDF file:", error);
      toast.error(
        `Failed to compress "${currentFile.name}". Please try again.`
      );
      setProgress(0);
    } finally {
      setTimeout(() => {
        setIsProcessing(false);
        setProgress(0);
      }, 1000);
    }
  };

  const handleCompressAnother = () => {
    setCompressionStats(null);
    setFiles([]);
    setShowConfetti(false);
    setIsProcessing(false);
    setProgress(0);
    setUploaderKey((prevKey) => prevKey + 1); // Force EnhancedFileUpload to re-mount and reset
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 300, damping: 24 },
    },
  };

  return (
    <MainLayout>
      <SuccessConfetti
        show={showConfetti}
        duration={3000}
        onComplete={() => setShowConfetti(false)}
      />

      <motion.div
        className="max-w-3xl mx-auto"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div className="mb-8 space-y-2" variants={itemVariants}>
          {" "}
          {/* */}
          <h1 className="text-3xl font-bold">Compress PDF</h1> {/* */}
          <p className="text-muted-foreground">
            Reduce the file size of your PDF documents while maintaining
            quality. Perfect for sharing via email or uploading to websites with
            file size limitations. {/* */}
          </p>
        </motion.div>

        {compressionStats ? (
          <motion.div
            className="mt-8 p-6 bg-muted/20 rounded-lg text-center"
            variants={itemVariants}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <h2 className="text-2xl font-semibold mb-2">
              Compression Complete!
            </h2>
            <p className="text-lg mb-4 text-muted-foreground">
              <span className="font-medium">
                {compressionStats.originalFilename}
              </span>{" "}
              has been compressed and downloaded.
            </p>
            <div className="space-y-2 text-left inline-block mb-6">
              <p>
                <strong>Original Size:</strong>{" "}
                {formatBytes(compressionStats.originalSize)}
              </p>
              <p>
                <strong>Compressed Size:</strong>{" "}
                {formatBytes(compressionStats.compressedSize)}
              </p>
              <p>
                <strong>Reduction:</strong> {compressionStats.ratio}
              </p>
            </div>
            <div>
              <Button onClick={handleCompressAnother} className="px-8">
                Compress Another PDF
              </Button>
            </div>
          </motion.div>
        ) : (
          <motion.div className="space-y-6" variants={itemVariants}>
            {" "}
            {/* */}
            <EnhancedFileUpload
              key={uploaderKey} // Force re-mount on key change to reset internal state
              acceptedFileTypes={{ "application/pdf": [".pdf"] }}
              maxFiles={1}
              onFilesSelected={handleFilesSelected}
              isProcessing={isProcessing}
              processingProgress={progress}
              replaceExisting={true}
            />
            {files.length > 0 && ( // Only show button if files are selected
              <div className="flex justify-end">
                {" "}
                {/* */}
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {" "}
                  {/* */}
                  <Button
                    onClick={handleCompress}
                    disabled={isProcessing} // files.length check implicitly handled by this block, was files.length === 0 || isProcessing
                    className="px-8"
                  >
                    {isProcessing
                      ? `Compressing (${progress}%)`
                      : "Compress PDF"}{" "}
                    {/* was {isProcessing ? "Compressing..." : "Compress PDF"} */}
                  </Button>
                </motion.div>
              </div>
            )}
          </motion.div>
        )}

        <motion.div
          className="mt-12 bg-muted/50 p-6 rounded-lg"
          variants={itemVariants}
        >
          <h2 className="text-xl font-semibold mb-4">About PDF Compression</h2>{" "}
          {/* */}
          <div className="space-y-4 text-sm text-muted-foreground">
            {" "}
            {/* */}
            <p>
              Our PDF compression tool reduces the file size of your PDF
              documents while preserving the quality of the content. This makes
              it easier to share your files via email, upload them to websites,
              or store them without taking up too much space. {/* */}
            </p>
            <p>
              The compression process works by optimizing images, removing
              unnecessary metadata, and applying other techniques to reduce file
              size without significant loss of quality. {/* */}
            </p>
            <p>
              <strong>Note:</strong> Your files are processed securely on our
              servers and are not stored permanently. They are automatically
              deleted after processing. {/* */}
            </p>
          </div>
        </motion.div>
      </motion.div>
    </MainLayout>
  );
}
