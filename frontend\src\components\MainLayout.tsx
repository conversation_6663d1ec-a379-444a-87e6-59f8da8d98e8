import { EnhancedHeader } from "@/components/EnhancedHeader";
import { Footer } from "@/components/Footer";
import { motion } from "framer-motion";
import React from "react";

interface MainLayoutProps {
  children: React.ReactNode;
}

export function MainLayout({ children }: MainLayoutProps) {
  return (
    <div className="flex flex-col min-h-screen">
      <EnhancedHeader />
      <motion.main
        className="flex-1 container mx-auto px-4 py-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.05, duration: 0.15 }}
      >
        {children}
      </motion.main>
      <Footer />
    </div>
  );
}
