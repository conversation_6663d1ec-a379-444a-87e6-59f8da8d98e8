"use client";

import {
  Accordion,
  Accordion<PERSON>ontent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { motion } from "framer-motion";

interface FAQProps {
  onCollapse?: () => void;
}

export function FAQ({ onCollapse }: FAQProps) {
  const faqItems = [
    {
      question: "Do you keep a copy of my processed files?",
      answer:
        "No, we do not store your files. All processing is done on-the-fly, and files are automatically deleted once processing is complete. Your privacy and data security are our top priorities.",
    },
    {
      question: "Are company files safe with your service?",
      answer:
        "Absolutely. We use secure processing methods and do not store any of your files on our servers. All processing happens in your browser or temporarily on our secure servers and is deleted immediately after processing is complete.",
    },
    {
      question: "How can I upload my files?",
      answer:
        "Simply drag and drop your files onto the upload area or click to browse your files. Our interface supports multiple file selection for tools that allow batch processing.",
    },
    {
      question: "What file formats are supported?",
      answer:
        "For PDF tools, we support PDF files and Word documents (.docx, .doc). For image tools, we support common formats including JPG, PNG, WebP, GIF, BMP, and TIFF.",
    },
    {
      question: "Is there a file size limit?",
      answer:
        "Yes, there is a 50MB limit per file to ensure optimal performance. For larger files, we recommend splitting them into smaller parts before uploading.",
    },
    {
      question: "Can I use this service on mobile devices?",
      answer:
        "Yes, our service is fully responsive and works on smartphones and tablets. The interface adapts to your screen size for the best experience.",
    },
  ];

  // Animation variants for staggered children
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.07,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 5 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <div className="w-full max-w-3xl mx-auto" id="faq-section">
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="bg-muted/30 p-6 rounded-lg"
      >
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-medium font-display">
            Frequently Asked Questions
          </h2>
          <button
            onClick={onCollapse}
            className="text-muted-foreground hover:text-primary transition-colors p-1 rounded-full hover:bg-muted/50"
            aria-label="Collapse FAQ"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-5 w-5"
            >
              <path d="M18 6 6 18" />
              <path d="m6 6 12 12" />
            </svg>
          </button>
        </div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Accordion type="single" collapsible className="w-full">
            {faqItems.map((item, index) => (
              <motion.div key={index} variants={itemVariants}>
                <AccordionItem
                  value={`item-${index}`}
                  className="border-b border-muted"
                >
                  <AccordionTrigger className="text-sm font-medium py-3 hover:no-underline">
                    {item.question}
                  </AccordionTrigger>
                  <AccordionContent className="text-sm text-muted-foreground">
                    {item.answer}
                  </AccordionContent>
                </AccordionItem>
              </motion.div>
            ))}
          </Accordion>
        </motion.div>
      </motion.div>
    </div>
  );
}
