"use client";

import { EnhancedFileUpload } from "@/components/EnhancedFileUpload";
import { MainLayout } from "@/components/MainLayout";
import { SuccessConfetti } from "@/components/SuccessConfetti";
import { Button } from "@/components/ui/button";
import { apiPost, downloadFromResponse } from "@/lib/api";
import { motion } from "framer-motion";
import { useState } from "react";
import { toast } from "sonner";

export default function WordToPDFPage() {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showConfetti, setShowConfetti] = useState(false);

  const handleFilesSelected = (selectedFiles: File[]) => {
    // With replaceExisting=true, we'll always get just the latest file
    setFiles(selectedFiles);
  };

  const handleConvert = async () => {
    if (files.length === 0) {
      toast.error("Please select a Word document to convert");
      return;
    }

    setIsProcessing(true);
    setProgress(10);

    try {
      const formData = new FormData();
      formData.append("file", files[0]);

      // Simulate progress for large files
      const file = files[0];
      const chunkSize = 1024 * 1024; // 1MB chunks

      if (file.size > chunkSize) {
        const chunks = Math.ceil(file.size / chunkSize);
        for (let j = 0; j < chunks; j++) {
          // Simulate chunk upload progress
          await new Promise((resolve) => setTimeout(resolve, 50));
          const fileProgress = (j + 1) / chunks;
          const overallProgress = 10 + 70 * fileProgress;
          setProgress(Math.min(80, Math.round(overallProgress)));
        }
      }

      setProgress(80);

      // Use the API utility instead of direct fetch
      const response = await apiPost("word-to-pdf", formData);

      setProgress(90);

      // Change the file extension from .docx or .doc to .pdf
      const fileName = files[0].name.replace(/\.(docx|doc)$/i, ".pdf");

      // Use the utility function to handle the download
      await downloadFromResponse(response, fileName);

      setProgress(100);
      setShowConfetti(true);
      toast.success("Word document converted to PDF successfully");
    } catch (error) {
      console.error("Error converting Word document to PDF:", error);
      toast.error("Failed to convert Word document to PDF. Please try again.");
    } finally {
      setTimeout(() => {
        setIsProcessing(false);
        setProgress(0);
      }, 500);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 300, damping: 24 },
    },
  };

  return (
    <MainLayout>
      <SuccessConfetti
        show={showConfetti}
        duration={3000}
        onComplete={() => setShowConfetti(false)}
      />

      <motion.div
        className="max-w-3xl mx-auto"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div className="mb-8 space-y-2" variants={itemVariants}>
          <h1 className="text-3xl font-bold">Word to PDF Converter</h1>
          <p className="text-muted-foreground">
            Convert Microsoft Word documents (.doc, .docx) to PDF format.
            Preserve formatting and layout for easy sharing and viewing on any
            device.
          </p>
        </motion.div>

        <motion.div className="space-y-6" variants={itemVariants}>
          <EnhancedFileUpload
            acceptedFileTypes={{
              "application/msword": [".doc"],
              "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
                [".docx"],
            }}
            maxFiles={1}
            onFilesSelected={handleFilesSelected}
            isProcessing={isProcessing}
            processingProgress={progress}
            replaceExisting={true}
          />

          {files.length > 0 && (
            <div className="flex justify-end">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  onClick={handleConvert}
                  disabled={isProcessing}
                  className="px-8"
                >
                  {isProcessing ? "Converting..." : "Convert to PDF"}
                </Button>
              </motion.div>
            </div>
          )}
        </motion.div>

        <motion.div
          className="mt-12 bg-muted/50 p-6 rounded-lg"
          variants={itemVariants}
        >
          <h2 className="text-xl font-semibold mb-4">
            About Word to PDF Conversion
          </h2>
          <div className="space-y-4 text-sm text-muted-foreground">
            <p>
              Our Word to PDF converter transforms Microsoft Word documents
              (.doc, .docx) into PDF files while preserving the original
              formatting, fonts, and layout. This ensures your document looks
              the same on any device or platform.
            </p>
            <p>
              PDF files are ideal for sharing documents because they maintain
              their appearance regardless of the software or operating system
              used to view them. They also cannot be easily edited, making them
              perfect for distributing final versions of documents.
            </p>
            <p>
              <strong>Note:</strong> Your files are processed securely on our
              servers and are not stored permanently. They are automatically
              deleted after processing.
            </p>
          </div>
        </motion.div>
      </motion.div>
    </MainLayout>
  );
}
