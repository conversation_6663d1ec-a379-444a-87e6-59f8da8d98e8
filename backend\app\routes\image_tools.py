import asyncio
import io
import logging
import os
import tempfile
from concurrent.futures import Thread<PERSON>oolExecutor
from functools import lru_cache
from pathlib import Path
from typing import List, Optional, Tuple

import aiofiles
import pillow_heif
from fastapi import APIRouter, File, Form, HTTPException, UploadFile
from fastapi.responses import StreamingResponse
from PIL import Image, ImageEnhance, ImageFilter, ImageOps
from PIL.Image import Resampling
from reportlab.lib.pagesizes import A4, letter
from reportlab.pdfgen import canvas

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

router = APIRouter()

pillow_heif.register_heif_opener()

# Thread pool for CPU-intensive operations
THREAD_POOL = ThreadPoolExecutor(max_workers=4)

SUPPORTED_FORMATS = {
    "jpg": "JPEG", "jpeg": "JPEG", "png": "PNG", "webp": "WEBP",
    "bmp": "BMP", "gif": "GIF", "tiff": "TIFF", "tif": "TIFF", 
    "heic": "JPEG", "heif": "JPEG"
}

# Optimized image processing settings
CHUNK_SIZE = 8192 * 8
MAX_PIXELS = 178956970  # PIL's default MAX_IMAGE_PIXELS
LANCZOS_FILTER = Resampling.LANCZOS
BICUBIC_FILTER = Resampling.BICUBIC

# Aggressive compression presets
COMPRESSION_PRESETS = {
    'PNG': {
        'small': {'compress_level': 9, 'optimize': True},
        'medium': {'compress_level': 6, 'optimize': True}, 
        'large': {'compress_level': 9, 'optimize': True}
    },
    'JPEG': {
        'small': {'quality': 90, 'optimize': True, 'progressive': True, 'subsampling': 0},
        'medium': {'quality': 80, 'optimize': True, 'progressive': True, 'subsampling': 0},
        'large': {'quality': 70, 'optimize': True, 'progressive': True, 'subsampling': 0}
    },
    'WEBP': {
        'small': {'quality': 85, 'method': 6, 'optimize': True},
        'medium': {'quality': 75, 'method': 6, 'optimize': True},
        'large': {'quality': 65, 'method': 6, 'optimize': True}
    }
}

@lru_cache(maxsize=32)
def get_format_from_ext(ext: str) -> str:
    """Cached format lookup to avoid repeated dictionary access."""
    return SUPPORTED_FORMATS.get(ext.lower(), "JPEG")

def is_image(filename: str) -> bool:
    """Fast image format validation."""
    if not filename:
        return False
    ext = filename.lower().split('.')[-1] if '.' in filename else ''
    return ext in SUPPORTED_FORMATS

async def save_upload_to_temp(file: UploadFile, temp_path: str) -> None:
    """Async file saving with optimized chunking."""
    async with aiofiles.open(temp_path, 'wb') as f:
        await file.seek(0)  # Ensure we're at the start
        while chunk := await file.read(CHUNK_SIZE):
            await f.write(chunk)

def process_image_sync(func, *args, **kwargs):
    """Synchronous wrapper for CPU-intensive image operations."""
    return func(*args, **kwargs)

def optimize_image_for_format(img: Image.Image, format_name: str) -> Image.Image:
    """Optimize image based on target format."""
    if format_name in ['JPEG', 'BMP'] and img.mode in ['RGBA', 'LA', 'P']:
        # Convert to RGB for formats that don't support transparency
        if img.mode == 'P':
            img = img.convert('RGBA')
        background = Image.new('RGB', img.size, (255, 255, 255))
        if img.mode == 'RGBA':
            background.paste(img, mask=img.split()[-1])
        else:
            background.paste(img)
        return background
    return img

def get_size_category(file_size: int) -> str:
    """Categorize file size for compression strategy."""
    if file_size > 10 * 1024 * 1024:  # > 10MB
        return 'large'
    elif file_size > 2 * 1024 * 1024:  # > 2MB
        return 'medium'
    else:
        return 'small'

def smart_resize(img: Image.Image, max_dimension: int = None) -> Image.Image:
    """Smart resizing to reduce file size while maintaining quality."""
    if max_dimension is None:
        return img
    
    width, height = img.size
    if max(width, height) <= max_dimension:
        return img
    
    # Calculate new dimensions maintaining aspect ratio
    if width > height:
        new_width = max_dimension
        new_height = int((height * max_dimension) / width)
    else:
        new_height = max_dimension
        new_width = int((width * max_dimension) / height)
    
    # Use high-quality resampling
    return img.resize((new_width, new_height), Image.Resampling.LANCZOS)

def optimize_image_for_format(img: Image.Image, format_name: str) -> Image.Image:
    """Advanced image optimization based on target format."""
    # Auto-orient image based on EXIF data
    img = ImageOps.exif_transpose(img)
    
    # Format-specific optimizations
    if format_name in ['JPEG', 'BMP'] and img.mode in ['RGBA', 'LA', 'P']:
        # Convert to RGB for formats that don't support transparency
        if img.mode == 'P':
            img = img.convert('RGBA')
        
        # Create white background for transparency
        background = Image.new('RGB', img.size, (255, 255, 255))
        if img.mode == 'RGBA':
            background.paste(img, mask=img.split()[-1])
        else:
            background.paste(img)
        return background
    
    elif format_name == 'PNG':
        # For PNG, convert palette images to RGBA for better compression
        if img.mode == 'P':
            img = img.convert('RGBA')
        # For grayscale images, ensure proper mode
        elif img.mode == 'L':
            pass  # Keep grayscale
        elif img.mode not in ['RGBA', 'RGB']:
            img = img.convert('RGBA')
    
    elif format_name == 'WEBP':
        # WEBP supports both RGB and RGBA efficiently
        if img.mode not in ['RGB', 'RGBA']:
            if 'transparency' in img.info or img.mode in ['RGBA', 'LA']:
                img = img.convert('RGBA')
            else:
                img = img.convert('RGB')
    
    return img

def compress_with_multiple_strategies(img: Image.Image, original_format: str, 
                                    target_format: str, file_size: int, 
                                    user_quality: int) -> tuple[bytes, dict]:
    """Try multiple compression strategies and return the best result."""
    strategies = []
    size_category = get_size_category(file_size)
    
    # Strategy 1: User-specified quality
    if target_format in COMPRESSION_PRESETS:
        preset = COMPRESSION_PRESETS[target_format][size_category].copy()
        if target_format in ['JPEG', 'WEBP']:
            preset['quality'] = user_quality
        strategies.append(('user_quality', preset))
    
    # Strategy 2: Preset-based compression
    if target_format in COMPRESSION_PRESETS:
        preset = COMPRESSION_PRESETS[target_format][size_category].copy()
        strategies.append(('preset', preset))
    
    # Strategy 3: Aggressive compression for large files
    if file_size > 5 * 1024 * 1024:  # > 5MB
        if target_format == 'JPEG':
            strategies.append(('aggressive', {
                'quality': 60, 'optimize': True, 'progressive': True, 'subsampling': 2
            }))
        elif target_format == 'PNG':
            strategies.append(('aggressive', {
                'compress_level': 9, 'optimize': True
            }))
        elif target_format == 'WEBP':
            strategies.append(('aggressive', {
                'quality': 55, 'method': 6, 'optimize': True
            }))
    
    # Strategy 4: Resize for very large images
    width, height = img.size
    max_pixels = width * height
    if max_pixels > 4000 * 4000:  # > 16MP
        resized_img = smart_resize(img, 3000)  # Resize to max 3000px
        strategies.append(('resize_3000', COMPRESSION_PRESETS.get(target_format, {}).get(size_category, {})))
        
        # Try even more aggressive resize for huge files
        if file_size > 20 * 1024 * 1024:  # > 20MB
            resized_img_2000 = smart_resize(img, 2000)
            strategies.append(('resize_2000', COMPRESSION_PRESETS.get(target_format, {}).get(size_category, {})))
    
    best_result = None
    best_size = float('inf')
    best_strategy = None
    
    for strategy_name, save_kwargs in strategies:
        try:
            current_img = img
            current_format = target_format
            
            # Handle special strategies
            if strategy_name.startswith('resize_'):
                max_dim = int(strategy_name.split('_')[1])
                current_img = smart_resize(img, max_dim)
                current_img = optimize_image_for_format(current_img, target_format)
            
            # Set format in save_kwargs
            save_kwargs = save_kwargs.copy()
            save_kwargs['format'] = current_format
            
            # Compress
            buffer = io.BytesIO()
            current_img.save(buffer, **save_kwargs)
            compressed_data = buffer.getvalue()
            compressed_size = len(compressed_data)
            
            if compressed_size < best_size:
                best_result = compressed_data
                best_size = compressed_size
                best_strategy = strategy_name
                
        except Exception as e:
            logger.warning(f"Strategy {strategy_name} failed: {e}")
            continue
    
    return best_result, {'strategy': best_strategy, 'size': best_size}

@router.post("/compress-image")
async def compress_image(
    file: UploadFile = File(...),
    quality: int = Form(85)
):
    """Ultra-high-performance image compression with advanced techniques."""
    if not is_image(file.filename):
        raise HTTPException(status_code=400, detail="Unsupported image format")

    if not 1 <= quality <= 100:
        raise HTTPException(status_code=400, detail="Quality must be between 1-100")

    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            input_path = os.path.join(temp_dir, "input")
            
            # Save uploaded file
            await save_upload_to_temp(file, input_path)
            original_size = os.path.getsize(input_path)
            
            def compression_task():
                with Image.open(input_path) as img:
                    # Get original format info
                    original_format = img.format or 'JPEG'
                    ext = file.filename.lower().split('.')[-1] if '.' in file.filename else 'jpg'
                    target_format = get_format_from_ext(ext)
                    
                    # Optimize image for target format
                    optimized_img = optimize_image_for_format(img, target_format)
                    
                    # Try multiple compression strategies
                    compressed_data, compression_info = compress_with_multiple_strategies(
                        optimized_img, original_format, target_format, original_size, quality
                    )
                    
                    return compressed_data, compression_info, target_format
            
            # Run compression in thread pool for blazing fast performance
            compressed_data, compression_info, target_format = await asyncio.get_event_loop().run_in_executor(
                THREAD_POOL, compression_task
            )
            
            compressed_size = len(compressed_data)
            
            # If compressed image is larger than original, return original
            if compressed_size >= original_size:
                logger.info(f"Compressed size ({compressed_size}) >= original ({original_size}), returning original")
                async with aiofiles.open(input_path, 'rb') as f:
                    original_data = await f.read()
                
                response_headers = {
                    "Content-Disposition": f"attachment; filename={file.filename}",
                    "X-Compression-Ratio": "0.00%",
                    "X-Original-Size": str(original_size),
                    "X-Compressed-Size": str(original_size),
                    "X-Compression-Strategy": "original_returned",
                    "Access-Control-Expose-Headers": (
                        "Content-Disposition, X-Compression-Ratio, "
                        "X-Original-Size, X-Compressed-Size, X-Compression-Strategy"
                    )
                }
                
                original_ext = file.filename.lower().split('.')[-1] if '.' in file.filename else 'jpg'
                return StreamingResponse(
                    io.BytesIO(original_data),
                    media_type=f"image/{original_ext}",
                    headers=response_headers
                )
            
            # Calculate compression ratio
            compression_ratio = (1 - (compressed_size / original_size)) * 100
            
            # Prepare response headers
            ext = target_format.lower()
            if ext == 'jpeg':
                ext = 'jpg'
            
            compressed_filename = f"compressed_{file.filename}"
            if '.' in compressed_filename:
                name, _ = compressed_filename.rsplit('.', 1)
                compressed_filename = f"{name}.{ext}"
            
            response_headers = {
                "Content-Disposition": f"attachment; filename={compressed_filename}",
                "X-Compression-Ratio": f"{compression_ratio:.2f}%",
                "X-Original-Size": str(original_size),
                "X-Compressed-Size": str(compressed_size),
                "X-Compression-Strategy": compression_info.get('strategy', 'unknown'),
                "Access-Control-Expose-Headers": (
                    "Content-Disposition, X-Compression-Ratio, "
                    "X-Original-Size, X-Compressed-Size, X-Compression-Strategy"
                )
            }

            return StreamingResponse(
                io.BytesIO(compressed_data),
                media_type=f"image/{ext}",
                headers=response_headers
            )
            
    except Exception as e:
        logger.error(f"Compression error: {e}")
        raise HTTPException(status_code=500, detail=f"Compression failed: {str(e)}")

@router.post("/convert-image")
async def convert_image(
    file: UploadFile = File(...),
    target_format: str = Form(...)
):
    """High-performance image format conversion."""
    if not is_image(file.filename):
        raise HTTPException(status_code=400, detail="Unsupported image format")
    
    target_format = target_format.lower()
    if target_format not in SUPPORTED_FORMATS:
        raise HTTPException(status_code=400, detail=f"Unsupported target format: {target_format}")

    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            input_path = os.path.join(temp_dir, "input")
            output_path = os.path.join(temp_dir, "output")
            
            await save_upload_to_temp(file, input_path)
            
            def convert_task():
                with Image.open(input_path) as img:
                    format_name = get_format_from_ext(target_format)
                    img = optimize_image_for_format(img, format_name)
                    
                    save_kwargs = {'format': format_name, 'optimize': True}
                    
                    if format_name == 'JPEG':
                        save_kwargs.update({
                            'quality': 90,
                            'progressive': True,
                            'subsampling': 0
                        })
                    elif format_name == 'PNG':
                        save_kwargs['compress_level'] = 6
                    elif format_name == 'WEBP':
                        save_kwargs.update({
                            'quality': 90,
                            'method': 6
                        })
                    
                    img.save(output_path, **save_kwargs)
            
            await asyncio.get_event_loop().run_in_executor(THREAD_POOL, convert_task)
            
            async with aiofiles.open(output_path, 'rb') as f:
                content = await f.read()
            
            original_name = os.path.splitext(file.filename)[0]
            return StreamingResponse(
                io.BytesIO(content),
                media_type=f"image/{target_format}",
                headers={"Content-Disposition": f"attachment; filename={original_name}.{target_format}"}
            )
            
    except Exception as e:
        logger.error(f"Conversion error: {e}")
        raise HTTPException(status_code=500, detail="Conversion failed")

@router.post("/resize-image")
async def resize_image(
    file: UploadFile = File(...),
    width: int = Form(...),
    height: int = Form(...),
    maintain_aspect_ratio: bool = Form(True)
):
    """High-performance image resizing with smart resampling."""
    if not is_image(file.filename):
        raise HTTPException(status_code=400, detail="Unsupported image format")
    
    if width <= 0 or height <= 0 or width > 50000 or height > 50000:
        raise HTTPException(status_code=400, detail="Invalid dimensions")

    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            input_path = os.path.join(temp_dir, "input")
            output_path = os.path.join(temp_dir, "output")
            
            await save_upload_to_temp(file, input_path)
            
            def resize_task():
                with Image.open(input_path) as img:
                    original_size = img.size
                    
                    if maintain_aspect_ratio:
                        img.thumbnail((width, height), LANCZOS_FILTER)
                    else:
                        # Choose optimal resampling based on scale
                        scale_factor = max(width / original_size[0], height / original_size[1])
                        resampling = LANCZOS_FILTER if scale_factor > 1 else BICUBIC_FILTER
                        img = img.resize((width, height), resampling)
                    
                    ext = file.filename.lower().split('.')[-1]
                    format_name = get_format_from_ext(ext)
                    img = optimize_image_for_format(img, format_name)
                    
                    save_kwargs = {'format': format_name, 'optimize': True}
                    if format_name == 'JPEG':
                        save_kwargs.update({'quality': 90, 'progressive': True})
                    
                    img.save(output_path, **save_kwargs)
            
            await asyncio.get_event_loop().run_in_executor(THREAD_POOL, resize_task)
            
            async with aiofiles.open(output_path, 'rb') as f:
                content = await f.read()
            
            ext = file.filename.lower().split('.')[-1]
            return StreamingResponse(
                io.BytesIO(content),
                media_type=f"image/{ext}",
                headers={"Content-Disposition": f"attachment; filename=resized_{file.filename}"}
            )
            
    except Exception as e:
        logger.error(f"Resize error: {e}")
        raise HTTPException(status_code=500, detail="Resize failed")

@router.post("/crop-image")
async def crop_image(
    file: UploadFile = File(...),
    left: int = Form(...),
    top: int = Form(...),
    right: int = Form(...),
    bottom: int = Form(...)
):
    """High-performance image cropping with bounds validation."""
    if not is_image(file.filename):
        raise HTTPException(status_code=400, detail="Unsupported image format")
    
    if left < 0 or top < 0 or left >= right or top >= bottom:
        raise HTTPException(status_code=400, detail="Invalid crop coordinates")

    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            input_path = os.path.join(temp_dir, "input")
            output_path = os.path.join(temp_dir, "output")
            
            await save_upload_to_temp(file, input_path)
            
            def crop_task():
                with Image.open(input_path) as img:
                    img_width, img_height = img.size
                    
                    # Validate and adjust crop coordinates
                    crop_box = (
                        max(0, left),
                        max(0, top),
                        min(right, img_width),
                        min(bottom, img_height)
                    )
                    
                    cropped = img.crop(crop_box)
                    
                    ext = file.filename.lower().split('.')[-1]
                    format_name = get_format_from_ext(ext)
                    cropped = optimize_image_for_format(cropped, format_name)
                    
                    save_kwargs = {'format': format_name, 'optimize': True}
                    if format_name == 'JPEG':
                        save_kwargs.update({'quality': 90, 'progressive': True})
                    
                    cropped.save(output_path, **save_kwargs)
            
            await asyncio.get_event_loop().run_in_executor(THREAD_POOL, crop_task)
            
            async with aiofiles.open(output_path, 'rb') as f:
                content = await f.read()
            
            ext = file.filename.lower().split('.')[-1]
            return StreamingResponse(
                io.BytesIO(content),
                media_type=f"image/{ext}",
                headers={"Content-Disposition": f"attachment; filename=cropped_{file.filename}"}
            )
            
    except Exception as e:
        logger.error(f"Crop error: {e}")
        raise HTTPException(status_code=500, detail="Crop failed")

@router.post("/upscale-image")
async def upscale_image(
    file: UploadFile = File(...),
    scale_factor: float = Form(2.0),
    enhance_quality: bool = Form(True)
):
    """Advanced image upscaling with quality enhancement."""
    if not is_image(file.filename):
        raise HTTPException(status_code=400, detail="Unsupported image format")
    
    if not 1.0 <= scale_factor <= 4.0:
        raise HTTPException(status_code=400, detail="Scale factor must be 1.0-4.0")

    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            input_path = os.path.join(temp_dir, "input")
            output_path = os.path.join(temp_dir, "output")
            
            await save_upload_to_temp(file, input_path)
            
            def upscale_task():
                with Image.open(input_path) as img:
                    original_size = img.size
                    new_size = (
                        int(original_size[0] * scale_factor),
                        int(original_size[1] * scale_factor)
                    )
                    
                    # Use LANCZOS for upscaling (best quality)
                    upscaled = img.resize(new_size, LANCZOS_FILTER)
                    
                    if enhance_quality:
                        # Multi-stage enhancement
                        # 1. Moderate sharpening
                        sharpener = ImageEnhance.Sharpness(upscaled)
                        upscaled = sharpener.enhance(1.2)
                        
                        # 2. Slight contrast enhancement
                        contrast = ImageEnhance.Contrast(upscaled)
                        upscaled = contrast.enhance(1.05)
                        
                        # 3. Color enhancement for photos
                        if scale_factor >= 2.0:
                            color = ImageEnhance.Color(upscaled)
                            upscaled = color.enhance(1.1)
                    
                    ext = file.filename.lower().split('.')[-1]
                    format_name = get_format_from_ext(ext)
                    upscaled = optimize_image_for_format(upscaled, format_name)
                    
                    save_kwargs = {'format': format_name, 'optimize': True}
                    if format_name == 'JPEG':
                        save_kwargs.update({
                            'quality': 95,  # High quality for upscaled images
                            'progressive': True,
                            'subsampling': 0
                        })
                    elif format_name == 'PNG':
                        save_kwargs['compress_level'] = 3  # Less compression for quality
                    
                    upscaled.save(output_path, **save_kwargs)
            
            await asyncio.get_event_loop().run_in_executor(THREAD_POOL, upscale_task)
            
            async with aiofiles.open(output_path, 'rb') as f:
                content = await f.read()
            
            ext = file.filename.lower().split('.')[-1]
            return StreamingResponse(
                io.BytesIO(content),
                media_type=f"image/{ext}",
                headers={"Content-Disposition": f"attachment; filename=upscaled_{file.filename}"}
            )
            
    except Exception as e:
        logger.error(f"Upscale error: {e}")
        raise HTTPException(status_code=500, detail="Upscale failed")

@router.post("/image-to-pdf")
async def image_to_pdf(
    files: List[UploadFile] = File(...),
    page_size: str = Form("A4"),
    fit_to_page: bool = Form(True)
):
    """High-performance batch image to PDF conversion."""
    if not files or len(files) > 100:  # Limit batch size
        raise HTTPException(status_code=400, detail="Invalid number of files (1-100)")
    
    for file in files:
        if not is_image(file.filename):
            raise HTTPException(status_code=400, detail=f"Unsupported format: {file.filename}")
    
    if page_size not in ["A4", "Letter"]:
        raise HTTPException(status_code=400, detail="Page size must be 'A4' or 'Letter'")

    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            # Save all files concurrently
            tasks = []
            input_paths = []
            
            for i, file in enumerate(files):
                input_path = os.path.join(temp_dir, f"input_{i}")
                input_paths.append(input_path)
                tasks.append(save_upload_to_temp(file, input_path))
            
            await asyncio.gather(*tasks)
            
            output_path = os.path.join(temp_dir, "output.pdf")
            
            def pdf_task():
                page_dimensions = A4 if page_size == "A4" else letter
                pdf = canvas.Canvas(output_path, pagesize=page_dimensions)
                page_width, page_height = page_dimensions
                
                for img_path in input_paths:
                    with Image.open(img_path) as img:
                        img = optimize_image_for_format(img, 'JPEG')
                        
                        img_width, img_height = img.size
                        
                        if fit_to_page:
                            # Calculate optimal fit
                            margin = 40
                            available_width = page_width - margin
                            available_height = page_height - margin
                            
                            scale_w = available_width / img_width
                            scale_h = available_height / img_height
                            scale = min(scale_w, scale_h)
                            
                            new_width = img_width * scale
                            new_height = img_height * scale
                            
                            x = (page_width - new_width) / 2
                            y = (page_height - new_height) / 2
                        else:
                            new_width, new_height = img_width, img_height
                            x = (page_width - new_width) / 2
                            y = (page_height - new_height) / 2
                        
                        # Save as JPEG for PDF compatibility
                        temp_jpg = f"{img_path}_temp.jpg"
                        img.save(temp_jpg, format="JPEG", quality=85, optimize=True)
                        
                        pdf.drawImage(temp_jpg, x, y, width=new_width, height=new_height)
                        pdf.showPage()
                
                pdf.save()
            
            await asyncio.get_event_loop().run_in_executor(THREAD_POOL, pdf_task)
            
            async with aiofiles.open(output_path, 'rb') as f:
                content = await f.read()
            
            filename = "images.pdf"
            if len(files) == 1:
                base_name = os.path.splitext(files[0].filename)[0]
                filename = f"{base_name}.pdf"
            
            return StreamingResponse(
                io.BytesIO(content),
                media_type="application/pdf",
                headers={"Content-Disposition": f"attachment; filename={filename}"}
            )
            
    except Exception as e:
        logger.error(f"PDF conversion error: {e}")
        raise HTTPException(status_code=500, detail="PDF conversion failed")