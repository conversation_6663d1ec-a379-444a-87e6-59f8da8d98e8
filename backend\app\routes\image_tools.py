import asyncio
import io
import logging
import os
import tempfile
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from functools import lru_cache
from pathlib import Path
from typing import List, Optional, Tuple

import aiofiles
from fastapi import APIRouter, File, Form, HTTPException, UploadFile
from fastapi.responses import StreamingResponse
from PIL import Image, ImageEnhance, ImageFilter, ImageOps
from PIL.Image import Resampling
from reportlab.lib.pagesizes import A4, letter
from reportlab.pdfgen import canvas

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

router = APIRouter()

# Thread pool for CPU-intensive operations
THREAD_POOL = ThreadPoolExecutor(max_workers=os.cpu_count() * 2)

SUPPORTED_FORMATS = {
    "jpg": "JPEG", "jpeg": "JPEG", "png": "PNG", "webp": "WEB<PERSON>",
    "bmp": "BMP", "gif": "GIF", "tiff": "TIFF", "tif": "TIFF"
}

# Optimized image processing settings
CHUNK_SIZE = 8192  # Smaller chunks for better memory management
MAX_PIXELS = 178956970  # PIL's default MAX_IMAGE_PIXELS
LANCZOS_FILTER = Resampling.LANCZOS
BICUBIC_FILTER = Resampling.BICUBIC

@lru_cache(maxsize=32)
def get_format_from_ext(ext: str) -> str:
    """Cached format lookup to avoid repeated dictionary access."""
    return SUPPORTED_FORMATS.get(ext.lower(), "JPEG")

def is_image(filename: str) -> bool:
    """Fast image format validation."""
    if not filename:
        return False
    ext = filename.lower().split('.')[-1]
    return ext in SUPPORTED_FORMATS

async def save_upload_to_temp(file: UploadFile, temp_path: str) -> None:
    """Async file saving with optimized chunking."""
    async with aiofiles.open(temp_path, 'wb') as f:
        while chunk := await file.read(CHUNK_SIZE):
            await f.write(chunk)

def process_image_sync(func, *args, **kwargs):
    """Synchronous wrapper for CPU-intensive image operations."""
    return func(*args, **kwargs)

def optimize_image_for_format(img: Image.Image, format_name: str) -> Image.Image:
    """Optimize image based on target format."""
    if format_name in ['JPEG', 'BMP'] and img.mode in ['RGBA', 'LA', 'P']:
        # Convert to RGB for formats that don't support transparency
        if img.mode == 'P':
            img = img.convert('RGBA')
        background = Image.new('RGB', img.size, (255, 255, 255))
        if img.mode == 'RGBA':
            background.paste(img, mask=img.split()[-1])
        else:
            background.paste(img)
        return background
    return img

def get_optimal_quality(file_size: int, target_format: str) -> int:
    """Calculate optimal quality based on file size and format."""
    if target_format not in ['JPEG']:
        return 95
    
    # Dynamic quality based on file size
    if file_size > 5 * 1024 * 1024:  # > 5MB
        return 75
    elif file_size > 1 * 1024 * 1024:  # > 1MB
        return 85
    else:
        return 90

@router.post("/compress-image")
async def compress_image(
    file: UploadFile = File(...),
    quality: int = Form(85)
):
    """High-performance image compression with adaptive quality."""
    if not is_image(file.filename):
        raise HTTPException(status_code=400, detail="Unsupported image format")

    if not 1 <= quality <= 100:
        raise HTTPException(status_code=400, detail="Quality must be between 1-100")

    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            input_path = os.path.join(temp_dir, "input")
            output_path = os.path.join(temp_dir, "output")
            
            await save_upload_to_temp(file, input_path)
            
            def compress_task():
                with Image.open(input_path) as img:
                    # Get file info for optimization
                    file_size = os.path.getsize(input_path)
                    ext = file.filename.lower().split('.')[-1]
                    format_name = get_format_from_ext(ext)
                    
                    # Optimize image for target format
                    img = optimize_image_for_format(img, format_name)
                    
                    # Use adaptive quality if not specified
                    final_quality = min(quality, get_optimal_quality(file_size, format_name))
                    
                    save_kwargs = {
                        'format': format_name,
                        'optimize': True,
                        'progressive': True if format_name == 'JPEG' else False
                    }
                    
                    if format_name == 'JPEG':
                        save_kwargs['quality'] = final_quality
                        save_kwargs['subsampling'] = 0  # Better quality
                    elif format_name == 'PNG':
                        save_kwargs['compress_level'] = 6  # Balanced compression
                    elif format_name == 'WEBP':
                        save_kwargs['quality'] = final_quality
                        save_kwargs['method'] = 6  # Better compression
                    
                    img.save(output_path, **save_kwargs)
            
            # Run compression in thread pool
            await asyncio.get_event_loop().run_in_executor(THREAD_POOL, compress_task)
            
            # Stream response
            async with aiofiles.open(output_path, 'rb') as f:
                content = await f.read()
            
            ext = file.filename.lower().split('.')[-1]
            return StreamingResponse(
                io.BytesIO(content),
                media_type=f"image/{ext}",
                headers={"Content-Disposition": f"attachment; filename=compressed_{file.filename}"}
            )
            
    except Exception as e:
        logger.error(f"Compression error: {e}")
        raise HTTPException(status_code=500, detail="Compression failed")

@router.post("/convert-image")
async def convert_image(
    file: UploadFile = File(...),
    target_format: str = Form(...)
):
    """High-performance image format conversion."""
    if not is_image(file.filename):
        raise HTTPException(status_code=400, detail="Unsupported image format")
    
    target_format = target_format.lower()
    if target_format not in SUPPORTED_FORMATS:
        raise HTTPException(status_code=400, detail=f"Unsupported target format: {target_format}")

    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            input_path = os.path.join(temp_dir, "input")
            output_path = os.path.join(temp_dir, "output")
            
            await save_upload_to_temp(file, input_path)
            
            def convert_task():
                with Image.open(input_path) as img:
                    format_name = get_format_from_ext(target_format)
                    img = optimize_image_for_format(img, format_name)
                    
                    save_kwargs = {'format': format_name, 'optimize': True}
                    
                    if format_name == 'JPEG':
                        save_kwargs.update({
                            'quality': 90,
                            'progressive': True,
                            'subsampling': 0
                        })
                    elif format_name == 'PNG':
                        save_kwargs['compress_level'] = 6
                    elif format_name == 'WEBP':
                        save_kwargs.update({
                            'quality': 90,
                            'method': 6
                        })
                    
                    img.save(output_path, **save_kwargs)
            
            await asyncio.get_event_loop().run_in_executor(THREAD_POOL, convert_task)
            
            async with aiofiles.open(output_path, 'rb') as f:
                content = await f.read()
            
            original_name = os.path.splitext(file.filename)[0]
            return StreamingResponse(
                io.BytesIO(content),
                media_type=f"image/{target_format}",
                headers={"Content-Disposition": f"attachment; filename={original_name}.{target_format}"}
            )
            
    except Exception as e:
        logger.error(f"Conversion error: {e}")
        raise HTTPException(status_code=500, detail="Conversion failed")

@router.post("/resize-image")
async def resize_image(
    file: UploadFile = File(...),
    width: int = Form(...),
    height: int = Form(...),
    maintain_aspect_ratio: bool = Form(True)
):
    """High-performance image resizing with smart resampling."""
    if not is_image(file.filename):
        raise HTTPException(status_code=400, detail="Unsupported image format")
    
    if width <= 0 or height <= 0 or width > 50000 or height > 50000:
        raise HTTPException(status_code=400, detail="Invalid dimensions")

    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            input_path = os.path.join(temp_dir, "input")
            output_path = os.path.join(temp_dir, "output")
            
            await save_upload_to_temp(file, input_path)
            
            def resize_task():
                with Image.open(input_path) as img:
                    original_size = img.size
                    
                    if maintain_aspect_ratio:
                        img.thumbnail((width, height), LANCZOS_FILTER)
                    else:
                        # Choose optimal resampling based on scale
                        scale_factor = max(width / original_size[0], height / original_size[1])
                        resampling = LANCZOS_FILTER if scale_factor > 1 else BICUBIC_FILTER
                        img = img.resize((width, height), resampling)
                    
                    ext = file.filename.lower().split('.')[-1]
                    format_name = get_format_from_ext(ext)
                    img = optimize_image_for_format(img, format_name)
                    
                    save_kwargs = {'format': format_name, 'optimize': True}
                    if format_name == 'JPEG':
                        save_kwargs.update({'quality': 90, 'progressive': True})
                    
                    img.save(output_path, **save_kwargs)
            
            await asyncio.get_event_loop().run_in_executor(THREAD_POOL, resize_task)
            
            async with aiofiles.open(output_path, 'rb') as f:
                content = await f.read()
            
            ext = file.filename.lower().split('.')[-1]
            return StreamingResponse(
                io.BytesIO(content),
                media_type=f"image/{ext}",
                headers={"Content-Disposition": f"attachment; filename=resized_{file.filename}"}
            )
            
    except Exception as e:
        logger.error(f"Resize error: {e}")
        raise HTTPException(status_code=500, detail="Resize failed")

@router.post("/crop-image")
async def crop_image(
    file: UploadFile = File(...),
    left: int = Form(...),
    top: int = Form(...),
    right: int = Form(...),
    bottom: int = Form(...)
):
    """High-performance image cropping with bounds validation."""
    if not is_image(file.filename):
        raise HTTPException(status_code=400, detail="Unsupported image format")
    
    if left < 0 or top < 0 or left >= right or top >= bottom:
        raise HTTPException(status_code=400, detail="Invalid crop coordinates")

    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            input_path = os.path.join(temp_dir, "input")
            output_path = os.path.join(temp_dir, "output")
            
            await save_upload_to_temp(file, input_path)
            
            def crop_task():
                with Image.open(input_path) as img:
                    img_width, img_height = img.size
                    
                    # Validate and adjust crop coordinates
                    crop_box = (
                        max(0, left),
                        max(0, top),
                        min(right, img_width),
                        min(bottom, img_height)
                    )
                    
                    cropped = img.crop(crop_box)
                    
                    ext = file.filename.lower().split('.')[-1]
                    format_name = get_format_from_ext(ext)
                    cropped = optimize_image_for_format(cropped, format_name)
                    
                    save_kwargs = {'format': format_name, 'optimize': True}
                    if format_name == 'JPEG':
                        save_kwargs.update({'quality': 90, 'progressive': True})
                    
                    cropped.save(output_path, **save_kwargs)
            
            await asyncio.get_event_loop().run_in_executor(THREAD_POOL, crop_task)
            
            async with aiofiles.open(output_path, 'rb') as f:
                content = await f.read()
            
            ext = file.filename.lower().split('.')[-1]
            return StreamingResponse(
                io.BytesIO(content),
                media_type=f"image/{ext}",
                headers={"Content-Disposition": f"attachment; filename=cropped_{file.filename}"}
            )
            
    except Exception as e:
        logger.error(f"Crop error: {e}")
        raise HTTPException(status_code=500, detail="Crop failed")

@router.post("/upscale-image")
async def upscale_image(
    file: UploadFile = File(...),
    scale_factor: float = Form(2.0),
    enhance_quality: bool = Form(True)
):
    """Advanced image upscaling with quality enhancement."""
    if not is_image(file.filename):
        raise HTTPException(status_code=400, detail="Unsupported image format")
    
    if not 1.0 <= scale_factor <= 4.0:
        raise HTTPException(status_code=400, detail="Scale factor must be 1.0-4.0")

    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            input_path = os.path.join(temp_dir, "input")
            output_path = os.path.join(temp_dir, "output")
            
            await save_upload_to_temp(file, input_path)
            
            def upscale_task():
                with Image.open(input_path) as img:
                    original_size = img.size
                    new_size = (
                        int(original_size[0] * scale_factor),
                        int(original_size[1] * scale_factor)
                    )
                    
                    # Use LANCZOS for upscaling (best quality)
                    upscaled = img.resize(new_size, LANCZOS_FILTER)
                    
                    if enhance_quality:
                        # Multi-stage enhancement
                        # 1. Moderate sharpening
                        sharpener = ImageEnhance.Sharpness(upscaled)
                        upscaled = sharpener.enhance(1.2)
                        
                        # 2. Slight contrast enhancement
                        contrast = ImageEnhance.Contrast(upscaled)
                        upscaled = contrast.enhance(1.05)
                        
                        # 3. Color enhancement for photos
                        if scale_factor >= 2.0:
                            color = ImageEnhance.Color(upscaled)
                            upscaled = color.enhance(1.1)
                    
                    ext = file.filename.lower().split('.')[-1]
                    format_name = get_format_from_ext(ext)
                    upscaled = optimize_image_for_format(upscaled, format_name)
                    
                    save_kwargs = {'format': format_name, 'optimize': True}
                    if format_name == 'JPEG':
                        save_kwargs.update({
                            'quality': 95,  # High quality for upscaled images
                            'progressive': True,
                            'subsampling': 0
                        })
                    elif format_name == 'PNG':
                        save_kwargs['compress_level'] = 3  # Less compression for quality
                    
                    upscaled.save(output_path, **save_kwargs)
            
            await asyncio.get_event_loop().run_in_executor(THREAD_POOL, upscale_task)
            
            async with aiofiles.open(output_path, 'rb') as f:
                content = await f.read()
            
            ext = file.filename.lower().split('.')[-1]
            return StreamingResponse(
                io.BytesIO(content),
                media_type=f"image/{ext}",
                headers={"Content-Disposition": f"attachment; filename=upscaled_{file.filename}"}
            )
            
    except Exception as e:
        logger.error(f"Upscale error: {e}")
        raise HTTPException(status_code=500, detail="Upscale failed")

@router.post("/image-to-pdf")
async def image_to_pdf(
    files: List[UploadFile] = File(...),
    page_size: str = Form("A4"),
    fit_to_page: bool = Form(True)
):
    """High-performance batch image to PDF conversion."""
    if not files or len(files) > 100:  # Limit batch size
        raise HTTPException(status_code=400, detail="Invalid number of files (1-100)")
    
    for file in files:
        if not is_image(file.filename):
            raise HTTPException(status_code=400, detail=f"Unsupported format: {file.filename}")
    
    if page_size not in ["A4", "Letter"]:
        raise HTTPException(status_code=400, detail="Page size must be 'A4' or 'Letter'")

    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            # Save all files concurrently
            tasks = []
            input_paths = []
            
            for i, file in enumerate(files):
                input_path = os.path.join(temp_dir, f"input_{i}")
                input_paths.append(input_path)
                tasks.append(save_upload_to_temp(file, input_path))
            
            await asyncio.gather(*tasks)
            
            output_path = os.path.join(temp_dir, "output.pdf")
            
            def pdf_task():
                page_dimensions = A4 if page_size == "A4" else letter
                pdf = canvas.Canvas(output_path, pagesize=page_dimensions)
                page_width, page_height = page_dimensions
                
                for img_path in input_paths:
                    with Image.open(img_path) as img:
                        img = optimize_image_for_format(img, 'JPEG')
                        
                        img_width, img_height = img.size
                        
                        if fit_to_page:
                            # Calculate optimal fit
                            margin = 40
                            available_width = page_width - margin
                            available_height = page_height - margin
                            
                            scale_w = available_width / img_width
                            scale_h = available_height / img_height
                            scale = min(scale_w, scale_h)
                            
                            new_width = img_width * scale
                            new_height = img_height * scale
                            
                            x = (page_width - new_width) / 2
                            y = (page_height - new_height) / 2
                        else:
                            new_width, new_height = img_width, img_height
                            x = (page_width - new_width) / 2
                            y = (page_height - new_height) / 2
                        
                        # Save as JPEG for PDF compatibility
                        temp_jpg = f"{img_path}_temp.jpg"
                        img.save(temp_jpg, format="JPEG", quality=85, optimize=True)
                        
                        pdf.drawImage(temp_jpg, x, y, width=new_width, height=new_height)
                        pdf.showPage()
                
                pdf.save()
            
            await asyncio.get_event_loop().run_in_executor(THREAD_POOL, pdf_task)
            
            async with aiofiles.open(output_path, 'rb') as f:
                content = await f.read()
            
            filename = "images.pdf"
            if len(files) == 1:
                base_name = os.path.splitext(files[0].filename)[0]
                filename = f"{base_name}.pdf"
            
            return StreamingResponse(
                io.BytesIO(content),
                media_type="application/pdf",
                headers={"Content-Disposition": f"attachment; filename={filename}"}
            )
            
    except Exception as e:
        logger.error(f"PDF conversion error: {e}")
        raise HTTPException(status_code=500, detail="PDF conversion failed")