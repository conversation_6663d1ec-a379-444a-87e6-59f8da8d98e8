/**
 * API utility functions for making requests to the backend
 * This centralizes all API calls and ensures they go through the Next.js API routes
 */

/**
 * Make a GET request to the API
 * @param endpoint - The API endpoint to call (without the /api/v1 prefix)
 * @param queryParams - Optional query parameters
 * @returns The response data
 */
export async function apiGet<T>(endpoint: string, queryParams?: Record<string, string>): Promise<T> {
  // Build the query string if query parameters are provided
  const queryString = queryParams 
    ? `?${new URLSearchParams(queryParams).toString()}`
    : '';
  
  // Make the request through the Next.js API route
  const response = await fetch(`/api/v1/${endpoint}${queryString}`);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `API error: ${response.status}`);
  }
  
  return response.json() as Promise<T>;
}

/**
 * Make a POST request to the API
 * @param endpoint - The API endpoint to call (without the /api/v1 prefix)
 * @param data - The data to send (FormData or JSON)
 * @returns The response (can be JSON data or a Blob for file downloads)
 */
export async function apiPost(endpoint: string, data: FormData | object): Promise<Response> {
  // Make the request through the Next.js API route
  const response = await fetch(`/api/v1/${endpoint}`, {
    method: 'POST',
    body: data instanceof FormData ? data : JSON.stringify(data),
    headers: data instanceof FormData ? {} : {
      'Content-Type': 'application/json',
    },
  });
  
  if (!response.ok) {
    try {
      const errorData = await response.json();
      throw new Error(errorData.detail || `API error: ${response.status}`);
    } catch (e) {
      // If the response is not JSON, throw a generic error
      throw new Error(`API error: ${response.status}`);
    }
  }
  
  return response;
}

/**
 * Download a file from a response
 * @param response - The response from an API call
 * @param defaultFilename - The default filename to use if none is provided in the response
 */
export async function downloadFromResponse(response: Response, defaultFilename: string): Promise<void> {
  // Get the filename from the Content-Disposition header if available
  const contentDisposition = response.headers.get("Content-Disposition");
  let filename = defaultFilename;
  
  if (contentDisposition) {
    const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
    if (filenameMatch && filenameMatch[1]) {
      filename = filenameMatch[1];
    }
  }

  // Convert the response to a blob and download it
  const blob = await response.blob();
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  window.URL.revokeObjectURL(url);
  document.body.removeChild(a);
}
