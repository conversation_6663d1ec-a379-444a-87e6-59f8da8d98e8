@echo off
echo Starting Smart Tools API...
echo.

REM Check if Python is installed
python --version > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Error: Python is not installed or not in PATH.
    echo Please install Python 3.7 or later and try again.
    pause
    exit /b 1
)

REM Install required packages if not already installed
echo Checking for required packages...
pip install -r requirements.txt > nul 2>&1

REM Start the API server
echo Starting API server...

REM Keep the window open
python run.py
