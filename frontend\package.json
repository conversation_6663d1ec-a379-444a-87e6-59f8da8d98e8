{"name": "smart-tools-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.2.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.12.0", "lucide-react": "^0.510.0", "next": "15.3.2", "next-themes": "^0.4.6", "react": "^19.0.0", "react-confetti": "^6.4.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-use": "^17.6.0", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tw-animate-css": "^1.2.9", "typescript": "^5"}}